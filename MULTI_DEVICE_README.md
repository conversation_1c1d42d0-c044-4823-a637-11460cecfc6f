# Auto Tool - <PERSON><PERSON><PERSON> năng Multi-Device

## Tổng quan

Auto Tool hiện đã hỗ trợ kết nối và điều khiển nhiều thiết bị Android cùng lúc. Tính năng này cho phép bạn:

- Kết nối với nhiều thiết bị Android/LDPlayer đồng thời
- Thực hiện các hành động song song trên tất cả thiết bị
- <PERSON><PERSON><PERSON> bộ hóa các hành động giữa các thiết bị
- Quản lý và giám sát trạng thái của từng thiết bị

## Các tính năng chính

### 1. MultiDeviceController

Class chính để quản lý nhiều thiết bị:

```python
from core.adb_controller import MultiDeviceController

# Khởi tạo controller
controller = MultiDeviceController()

# Quét thiết bị có sẵn
devices = controller.scan_devices()

# Thêm thiết bị vào quản lý
controller.add_device("127.0.0.1:5555")
controller.add_device("127.0.0.1:5556")

# Lấy danh sách thiết bị đang hoạt động
active_devices = controller.get_active_devices()
```

### 2. Hành động song song

Thực hiện cùng một hành động trên tất cả thiết bị:

```python
# Chụp màn hình tất cả thiết bị
screenshots = controller.take_screenshots_all()

# Click tọa độ trên tất cả thiết bị
controller.tap_all_devices(500, 300)

# Vuốt trên tất cả thiết bị
controller.swipe_all_devices(100, 100, 900, 100, 500)

# Nhập text trên tất cả thiết bị
controller.input_text_all_devices("Hello World")
```

### 3. Hệ thống đồng bộ hóa

Hỗ trợ 4 chế độ đồng bộ:

#### a) PARALLEL (Song song)
Mỗi thiết bị thực hiện độc lập:
```python
from core.device_sync import SyncMode

controller.set_sync_mode(SyncMode.PARALLEL)
controller.sync_tap(400, 400)
```

#### b) SYNCHRONIZED (Đồng bộ hoàn toàn)
Tất cả thiết bị thực hiện cùng lúc:
```python
controller.set_sync_mode(SyncMode.SYNCHRONIZED)
controller.sync_tap(400, 400)
```

#### c) LEADER_FOLLOWER (Dẫn đầu - Theo sau)
Một thiết bị dẫn đầu, các thiết bị khác theo sau:
```python
controller.set_sync_mode(SyncMode.LEADER_FOLLOWER, "127.0.0.1:5555")
controller.sync_tap(400, 400, delay=0.5)  # Followers chậm 0.5s
```

#### d) ROUND_ROBIN (Lần lượt)
Thực hiện lần lượt từng thiết bị:
```python
controller.set_sync_mode(SyncMode.ROUND_ROBIN)
controller.sync_tap(400, 400, delay=1.0)  # Mỗi thiết bị cách nhau 1s
```

### 4. Giao diện người dùng

Giao diện đã được cập nhật để hỗ trợ multi-device:

- **Quét thiết bị**: Tự động tìm các thiết bị Android có sẵn
- **Quản lý thiết bị**: Thêm/xóa thiết bị khỏi danh sách quản lý
- **Hiển thị trạng thái**: Theo dõi trạng thái kết nối của từng thiết bị
- **Chụp tất cả**: Chụp màn hình tất cả thiết bị cùng lúc
- **Chế độ đồng bộ**: Chuyển đổi giữa các chế độ đồng bộ

## Cách sử dụng

### 1. Khởi động ứng dụng

```bash
python main.py
```

### 2. Quét và thêm thiết bị

1. Click nút "Quét thiết bị" để tìm các thiết bị có sẵn
2. Chọn thiết bị từ dropdown "Thiết bị có sẵn"
3. Click "Thêm thiết bị" để thêm vào danh sách quản lý
4. Thiết bị sẽ xuất hiện trong danh sách "Thiết bị đã kết nối"

### 3. Chọn thiết bị hiện tại

- Click vào thiết bị trong danh sách "Thiết bị đã kết nối"
- Thiết bị được chọn sẽ hiển thị màn hình trong khung preview

### 4. Sử dụng tính năng multi-device

- **Chụp tất cả**: Chụp màn hình tất cả thiết bị
- **Đồng bộ hành động**: Bật/tắt chế độ đồng bộ
- **Auto features**: Các tính năng auto sẽ chạy trên thiết bị được chọn hoặc tất cả thiết bị

### 5. Chạy demo

```bash
python demo_multi_device.py
```

Demo sẽ hướng dẫn bạn qua tất cả các tính năng multi-device.

## Cấu hình thiết bị

### LDPlayer

1. Mở LDPlayer Manager
2. Tạo nhiều instance LDPlayer
3. Khởi động các instance
4. Các instance sẽ tự động được phát hiện trên các port 5555-5585

### Thiết bị thật

1. Bật Developer Options và USB Debugging
2. Kết nối qua USB hoặc WiFi ADB
3. Thiết bị sẽ xuất hiện trong danh sách quét

## Lưu ý quan trọng

### Hiệu năng

- Số lượng thiết bị đồng thời ảnh hưởng đến hiệu năng
- Khuyến nghị tối đa 5-10 thiết bị cùng lúc
- Chế độ SYNCHRONIZED tốn tài nguyên nhất

### Đồng bộ hóa

- Chế độ PARALLEL nhanh nhất nhưng không đồng bộ
- Chế độ SYNCHRONIZED chậm nhất nhưng đồng bộ hoàn toàn
- Chế độ LEADER_FOLLOWER cân bằng giữa tốc độ và đồng bộ

### Xử lý lỗi

- Thiết bị mất kết nối sẽ tự động được loại khỏi danh sách active
- Sử dụng "Làm mới" để kiểm tra lại trạng thái thiết bị
- Kiểm tra thống kê đồng bộ để theo dõi lỗi

## API Reference

### MultiDeviceController

```python
# Quản lý thiết bị
scan_devices() -> List[str]
add_device(device_serial: str) -> bool
remove_device(device_serial: str) -> bool
get_active_devices() -> List[str]

# Hành động song song
tap_all_devices(x: int, y: int)
swipe_all_devices(x1: int, y1: int, x2: int, y2: int, duration: int = 300)
input_text_all_devices(text: str)
key_event_all_devices(keycode: int)

# Đồng bộ hóa
set_sync_mode(mode: SyncMode, leader_device: str = None)
sync_tap(x: int, y: int, target_devices: List[str] = None, delay: float = 0)
sync_swipe(x1: int, y1: int, x2: int, y2: int, duration: int = 300, 
           target_devices: List[str] = None, delay: float = 0)

# Thống kê
get_sync_stats() -> Dict
get_device_sync_status() -> Dict
```

### DeviceSynchronizer

```python
# Đăng ký thiết bị
register_device(device_id: str, device_controller)
unregister_device(device_id: str)

# Thực hiện hành động
execute_action(action: SyncAction, target_devices: List[str] = None)

# Cấu hình
set_sync_mode(mode: SyncMode, leader_device: str = None)
register_action_handler(action_type: str, handler: Callable)
```

## Troubleshooting

### Thiết bị không được phát hiện

1. Kiểm tra ADB connection: `adb devices`
2. Restart ADB server: `adb kill-server && adb start-server`
3. Kiểm tra USB Debugging đã bật
4. Với LDPlayer: Kiểm tra instance đã khởi động

### Hành động không đồng bộ

1. Kiểm tra chế độ đồng bộ hiện tại
2. Verify tất cả thiết bị đều active
3. Kiểm tra thống kê lỗi đồng bộ
4. Thử giảm số lượng thiết bị

### Hiệu năng chậm

1. Giảm số lượng thiết bị đồng thời
2. Sử dụng chế độ PARALLEL thay vì SYNCHRONIZED
3. Tăng delay giữa các hành động
4. Kiểm tra tài nguyên hệ thống

## Kết luận

Tính năng multi-device của Auto Tool mang lại khả năng mở rộng mạnh mẽ cho việc tự động hóa trên nhiều thiết bị Android. Với hệ thống đồng bộ hóa linh hoạt và giao diện quản lý trực quan, bạn có thể dễ dàng điều khiển và giám sát nhiều thiết bị cùng lúc.

Để biết thêm chi tiết, hãy tham khảo code trong các file:
- `core/adb_controller.py` - MultiDeviceController
- `core/device_sync.py` - DeviceSynchronizer  
- `ui/main_window.py` - Giao diện multi-device
- `demo_multi_device.py` - Ví dụ sử dụng
