#!/usr/bin/env python3
"""
Demo script để minh họa tính năng multi-device của Auto Tool
"""

import sys
import time
from core.adb_controller import MultiDeviceController
from core.device_sync import SyncMode
from core.image_recognition import ImageRecognition


def demo_basic_multi_device():
    """Demo cơ bản về quản lý nhiều thiết bị"""
    print("=== DEMO QUẢN LÝ NHIỀU THIẾT BỊ ===")
    
    # Khởi tạo controller
    controller = MultiDeviceController()
    
    # Quét thiết bị
    print("1. Quét thiết bị có sẵn...")
    devices = controller.scan_devices()
    print(f"Tìm thấy {len(devices)} thiết bị: {devices}")
    
    # Thêm thiết bị
    print("\n2. Thêm thiết bị vào quản lý...")
    for device in devices[:3]:  # Chỉ thêm tối đa 3 thiết bị
        success = controller.add_device(device)
        print(f"Thêm {device}: {'Thành công' if success else 'Thất bại'}")
    
    # Hiển thị thiết bị đang quản lý
    print("\n3. Thiết bị đang quản lý:")
    active_devices = controller.get_active_devices()
    for device in active_devices:
        status = controller.check_device_status(device)
        print(f"  - {device}: {'Hoạt động' if status else 'Mất kết nối'}")
    
    return controller


def demo_parallel_actions(controller):
    """Demo thực hiện hành động song song"""
    print("\n=== DEMO HÀNH ĐỘNG SONG SONG ===")
    
    active_devices = controller.get_active_devices()
    if not active_devices:
        print("Không có thiết bị nào hoạt động!")
        return
    
    print("1. Chụp màn hình tất cả thiết bị...")
    screenshots = controller.take_screenshots_all()
    print(f"Đã chụp màn hình {len(screenshots)} thiết bị")
    
    print("\n2. Click tọa độ (500, 300) trên tất cả thiết bị...")
    controller.tap_all_devices(500, 300)
    time.sleep(1)
    
    print("\n3. Vuốt từ (100, 100) đến (900, 100) trên tất cả thiết bị...")
    controller.swipe_all_devices(100, 100, 900, 100, 500)
    time.sleep(1)
    
    print("\n4. Nhập text 'Hello Multi-Device' trên tất cả thiết bị...")
    controller.input_text_all_devices("Hello Multi-Device")
    time.sleep(1)


def demo_synchronized_actions(controller):
    """Demo thực hiện hành động đồng bộ"""
    print("\n=== DEMO HÀNH ĐỘNG ĐỒNG BỘ ===")
    
    # Thiết lập chế độ đồng bộ
    print("1. Thiết lập chế độ đồng bộ hoàn toàn...")
    controller.set_sync_mode(SyncMode.SYNCHRONIZED)
    
    print("\n2. Thực hiện tap đồng bộ tại (400, 400)...")
    controller.sync_tap(400, 400)
    time.sleep(1)
    
    print("\n3. Thực hiện swipe đồng bộ...")
    controller.sync_swipe(200, 200, 800, 200, 300)
    time.sleep(1)
    
    print("\n4. Nhập text đồng bộ...")
    controller.sync_input_text("Synchronized Text")
    time.sleep(1)


def demo_leader_follower_mode(controller):
    """Demo chế độ leader-follower"""
    print("\n=== DEMO CHẾ ĐỘ LEADER-FOLLOWER ===")
    
    active_devices = controller.get_active_devices()
    if len(active_devices) < 2:
        print("Cần ít nhất 2 thiết bị để demo chế độ này!")
        return
    
    # Chọn leader
    leader = active_devices[0]
    print(f"1. Thiết lập {leader} làm leader...")
    controller.set_sync_mode(SyncMode.LEADER_FOLLOWER, leader)
    
    print("\n2. Thực hiện hành động với leader dẫn đầu...")
    controller.sync_tap(300, 300, delay=0.5)  # Leader thực hiện trước, followers sau 0.5s
    time.sleep(2)
    
    controller.sync_swipe(100, 500, 900, 500, 400, delay=0.3)
    time.sleep(2)


def demo_round_robin_mode(controller):
    """Demo chế độ round-robin"""
    print("\n=== DEMO CHẾ ĐỘ ROUND-ROBIN ===")
    
    print("1. Thiết lập chế độ round-robin...")
    controller.set_sync_mode(SyncMode.ROUND_ROBIN)
    
    print("\n2. Thực hiện hành động lần lượt từng thiết bị...")
    controller.sync_tap(250, 250, delay=1.0)  # Mỗi thiết bị cách nhau 1 giây
    time.sleep(3)
    
    controller.sync_key_event(4, delay=0.5)  # Back button, mỗi thiết bị cách nhau 0.5s
    time.sleep(2)


def demo_sync_statistics(controller):
    """Demo thống kê đồng bộ"""
    print("\n=== THỐNG KÊ ĐỒNG BỘ ===")
    
    stats = controller.get_sync_stats()
    print("Thống kê tổng quan:")
    print(f"  - Tổng số hành động: {stats['total_actions']}")
    print(f"  - Đồng bộ thành công: {stats['successful_syncs']}")
    print(f"  - Đồng bộ thất bại: {stats['failed_syncs']}")
    print(f"  - Thời gian đồng bộ trung bình: {stats['avg_sync_time']:.3f}s")
    
    print("\nTrạng thái từng thiết bị:")
    device_status = controller.get_device_sync_status()
    for device_id, status in device_status.items():
        print(f"  - {device_id}:")
        print(f"    + Trạng thái: {status['status']}")
        print(f"    + Số hành động: {status['action_count']}")
        print(f"    + Lỗi đồng bộ: {status['sync_errors']}")
        print(f"    + Hành động cuối: {status['last_action']}")


def demo_image_recognition_multi_device(controller):
    """Demo nhận diện hình ảnh trên nhiều thiết bị"""
    print("\n=== DEMO NHẬN DIỆN HÌNH ẢNH MULTI-DEVICE ===")
    
    # Khởi tạo image recognition
    image_rec = ImageRecognition()
    
    # Chụp màn hình tất cả thiết bị
    print("1. Chụp màn hình tất cả thiết bị...")
    screenshots = controller.take_screenshots_all()
    
    # Giả lập tìm kiếm template trên từng thiết bị
    print("\n2. Tìm kiếm template 'boss_icon' trên từng thiết bị...")
    for device_id, screenshot in screenshots.items():
        if screenshot is not None:
            # Trong thực tế, bạn sẽ load template thật
            # pos = image_rec.find_template(screenshot, 'boss_icon')
            # if pos:
            #     print(f"  - {device_id}: Tìm thấy tại {pos}")
            #     controller.execute_on_device(device_id, 'tap', pos[0], pos[1])
            # else:
            #     print(f"  - {device_id}: Không tìm thấy")
            print(f"  - {device_id}: Screenshot kích thước {screenshot.shape}")
        else:
            print(f"  - {device_id}: Không thể chụp màn hình")


def main():
    """Hàm main để chạy tất cả demo"""
    print("🚀 DEMO AUTO TOOL MULTI-DEVICE 🚀")
    print("=" * 50)
    
    try:
        # Demo cơ bản
        controller = demo_basic_multi_device()
        
        if not controller.get_active_devices():
            print("\n❌ Không có thiết bị nào hoạt động. Kết thúc demo.")
            return
        
        # Đợi user xác nhận
        input("\n📱 Nhấn Enter để tiếp tục demo hành động song song...")
        demo_parallel_actions(controller)
        
        input("\n🔄 Nhấn Enter để tiếp tục demo hành động đồng bộ...")
        demo_synchronized_actions(controller)
        
        if len(controller.get_active_devices()) >= 2:
            input("\n👑 Nhấn Enter để tiếp tục demo chế độ leader-follower...")
            demo_leader_follower_mode(controller)
            
            input("\n🔄 Nhấn Enter để tiếp tục demo chế độ round-robin...")
            demo_round_robin_mode(controller)
        
        input("\n📊 Nhấn Enter để xem thống kê...")
        demo_sync_statistics(controller)
        
        input("\n🖼️ Nhấn Enter để demo nhận diện hình ảnh...")
        demo_image_recognition_multi_device(controller)
        
        print("\n✅ Demo hoàn thành!")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Demo bị dừng bởi người dùng.")
    except Exception as e:
        print(f"\n❌ Lỗi trong quá trình demo: {e}")
    
    print("\n👋 Cảm ơn bạn đã sử dụng Auto Tool Multi-Device!")


if __name__ == "__main__":
    main()
