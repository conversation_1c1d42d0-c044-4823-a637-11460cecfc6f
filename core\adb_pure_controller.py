import subprocess
import time
import cv2
import numpy as np
from PIL import Image
import io
import re

class PureADBController:
    """ADB Controller sử dụng subprocess thay vì adbutils"""
    
    def __init__(self, device_serial=None):
        self.device_serial = device_serial
        self.adb_path = "adb"  # Đảm bảo adb trong PATH
        self.connected = False
        self.connect()
    
    def run_adb_command(self, command):
        """Chạy lệnh ADB"""
        cmd = [self.adb_path]
        
        if self.device_serial:
            cmd.extend(["-s", self.device_serial])
        
        cmd.extend(command.split())
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            return result.stdout, result.stderr, result.returncode
        except subprocess.TimeoutExpired:
            return "", "Command timeout", 1
        except Exception as e:
            return "", str(e), 1
    
    def connect(self):
        """Kết nối với thiết bị"""
        try:
            # <PERSON><PERSON><PERSON> không có device_serial, t<PERSON><PERSON> thi<PERSON><PERSON> bị đầu tiên
            if not self.device_serial:
                stdout, _, _ = self.run_adb_command("devices")
                lines = stdout.strip().split('\n')[1:]  # Bỏ header
                
                for line in lines:
                    if '\tdevice' in line:
                        self.device_serial = line.split('\t')[0]
                        break
            
            if self.device_serial:
                # Kiểm tra kết nối
                stdout, _, returncode = self.run_adb_command("get-state")
                if returncode == 0 and "device" in stdout:
                    self.connected = True
                    print(f"Đã kết nối với thiết bị: {self.device_serial}")
                    return True
            
            # Thử kết nối với LDPlayer ports
            for port in range(5555, 5586):
                device = f"127.0.0.1:{port}"
                stdout, stderr, returncode = subprocess.run(
                    [self.adb_path, "connect", device],
                    capture_output=True, text=True
                ).returncode
                
                if returncode == 0:
                    self.device_serial = device
                    self.connected = True
                    print(f"Đã kết nối với LDPlayer: {device}")
                    return True
            
            print("Không tìm thấy thiết bị")
            return False
            
        except Exception as e:
            print(f"Lỗi kết nối: {e}")
            return False
    
    def tap(self, x, y):
        """Click vào tọa độ x, y"""
        if self.connected:
            self.run_adb_command(f"shell input tap {x} {y}")
            time.sleep(0.1)
    
    def swipe(self, x1, y1, x2, y2, duration=300):
        """Vuốt từ (x1,y1) đến (x2,y2)"""
        if self.connected:
            self.run_adb_command(f"shell input swipe {x1} {y1} {x2} {y2} {duration}")
            time.sleep(0.1)
    
    def long_press(self, x, y, duration=1000):
        """Giữ click tại vị trí"""
        if self.connected:
            self.swipe(x, y, x, y, duration)
    
    def screenshot(self):
        """Chụp màn hình và trả về ảnh numpy array"""
        if not self.connected:
            return None
        
        try:
            # Sử dụng exec-out để lấy screenshot trực tiếp
            cmd = [self.adb_path]
            if self.device_serial:
                cmd.extend(["-s", self.device_serial])
            cmd.extend(["exec-out", "screencap", "-p"])
            
            result = subprocess.run(cmd, capture_output=True, timeout=5)
            
            if result.returncode == 0 and result.stdout:
                # Convert to PIL Image
                img = Image.open(io.BytesIO(result.stdout))
                # Convert to numpy array
                img_array = np.array(img)
                # Convert RGB to BGR for OpenCV
                img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
                return img_bgr
            else:
                print("Lỗi chụp màn hình")
                return None
                
        except Exception as e:
            print(f"Lỗi screenshot: {e}")
            return None
    
    def input_text(self, text):
        """Nhập text"""
        if self.connected:
            # Escape special characters
            text = text.replace(' ', '%s')
            text = text.replace('&', '\\&')
            text = text.replace('<', '\\<')
            text = text.replace('>', '\\>')
            text = text.replace("'", "\\'")
            text = text.replace('"', '\\"')
            
            self.run_adb_command(f'shell input text "{text}"')
            time.sleep(0.1)
    
    def key_event(self, keycode):
        """Gửi phím đặc biệt
        KEYCODE_BACK = 4
        KEYCODE_HOME = 3
        KEYCODE_MENU = 82
        KEYCODE_ENTER = 66
        """
        if self.connected:
            self.run_adb_command(f"shell input keyevent {keycode}")
            time.sleep(0.1)
    
    def get_screen_size(self):
        """Lấy kích thước màn hình"""
        if self.connected:
            stdout, _, _ = self.run_adb_command("shell wm size")
            
            # Parse output: "Physical size: 1280x720"
            match = re.search(r'Physical size: (\d+)x(\d+)', stdout)
            if match:
                width = int(match.group(1))
                height = int(match.group(2))
                return width, height
        
        return None, None
    
    def get_device_list(self):
        """Lấy danh sách thiết bị đang kết nối"""
        stdout, _, _ = self.run_adb_command("devices")
        devices = []
        
        lines = stdout.strip().split('\n')[1:]  # Bỏ header
        for line in lines:
            if '\tdevice' in line:
                device_id = line.split('\t')[0]
                devices.append(device_id)
        
        return devices
    
    def install_apk(self, apk_path):
        """Cài đặt APK"""
        if self.connected:
            stdout, stderr, returncode = self.run_adb_command(f"install {apk_path}")
            return returncode == 0
        return False
    
    def start_app(self, package_name, activity_name):
        """Khởi động app"""
        if self.connected:
            self.run_adb_command(
                f"shell am start -n {package_name}/{activity_name}"
            )
    
    def stop_app(self, package_name):
        """Dừng app"""
        if self.connected:
            self.run_adb_command(f"shell am force-stop {package_name}")
    
    def clear_app_data(self, package_name):
        """Xóa data của app"""
        if self.connected:
            self.run_adb_command(f"shell pm clear {package_name}")