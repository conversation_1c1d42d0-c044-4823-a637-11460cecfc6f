import subprocess
import time
from adbutils import adb
import cv2
import numpy as np
from io import BytesIO
import threading
from typing import Dict, List, Optional, Tuple
from .device_sync import DeviceSynchronizer, SyncAction, SyncMode
from .device_sync import (default_tap_handler, default_swipe_handler,
                         default_input_text_handler, default_key_event_handler)

class ADBController:
    def __init__(self, device_serial=None):
        """Khởi tạo kết nối ADB với LDPlayer"""
        self.device = None
        self.device_serial = device_serial
        self.connect()
    
    def connect(self):
        """Kết nối với LDPlayer"""
        try:
            # LDPlayer thường chạy ở port 5555-5585
            if not self.device_serial:
                # Tìm LDPlayer đang chạy
                devices = adb.device_list()
                for d in devices:
                    if '5555' in d.serial or 'emulator' in d.serial:
                        self.device = d
                        self.device_serial = d.serial
                        break
            else:
                self.device = adb.device(self.device_serial)
            
            if self.device:
                print(f"Đ<PERSON> kết nối với thiết bị: {self.device_serial}")
                return True
            else:
                print("Không tìm thấy LDPlayer")
                return False
        except Exception as e:
            print(f"Lỗi kết nối: {e}")
            return False
    
    def tap(self, x, y):
        """Click vào tọa độ x, y"""
        if self.device:
            self.device.shell(f"input tap {x} {y}")
            time.sleep(0.1)
    
    def swipe(self, x1, y1, x2, y2, duration=300):
        """Vuốt từ (x1,y1) đến (x2,y2)"""
        if self.device:
            self.device.shell(f"input swipe {x1} {y1} {x2} {y2} {duration}")
            time.sleep(0.1)
    
    def long_press(self, x, y, duration=1000):
        """Giữ click tại vị trí"""
        if self.device:
            self.swipe(x, y, x, y, duration)
    
    def screenshot(self):
        """Chụp màn hình và trả về ảnh numpy array"""
        if self.device:
            # Lấy ảnh PIL từ adbutils
            pil_image = self.device.screenshot()

            # Chuyển PIL.Image sang bytes (dạng PNG)
            buf = BytesIO()
            pil_image.save(buf, format="PNG")
            png_data = buf.getvalue()

            # Chuyển bytes thành numpy array
            img_array = np.frombuffer(png_data, np.uint8)

            # Giải mã thành ảnh OpenCV
            img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)

            return img
        return None
    
    def input_text(self, text):
        """Nhập text"""
        if self.device:
            # Escape special characters
            text = text.replace(' ', '%s')
            self.device.shell(f"input text '{text}'")
            time.sleep(0.1)
    
    def key_event(self, keycode):
        """Gửi phím đặc biệt
        KEYCODE_BACK = 4
        KEYCODE_HOME = 3
        KEYCODE_MENU = 82
        """
        if self.device:
            self.device.shell(f"input keyevent {keycode}")
            time.sleep(0.1)
    
    def get_screen_size(self):
        """Lấy kích thước màn hình"""
        if self.device:
            output = self.device.shell("wm size")
            # Parse output: "Physical size: 1280x720"
            if "Physical size:" in output:
                size_str = output.split("Physical size:")[1].strip()
                width, height = map(int, size_str.split('x'))
                return width, height
        return None, None


class MultiDeviceController:
    """Controller để quản lý nhiều thiết bị Android cùng lúc"""

    def __init__(self):
        self.devices: Dict[str, ADBController] = {}
        self.device_status: Dict[str, bool] = {}
        self.active_devices: List[str] = []
        self.lock = threading.Lock()

        # Khởi tạo hệ thống đồng bộ
        self.synchronizer = DeviceSynchronizer()
        self._setup_sync_handlers()

    def scan_devices(self) -> List[str]:
        """Quét tìm tất cả thiết bị Android có sẵn"""
        try:
            devices = adb.device_list()
            device_serials = []

            for device in devices:
                device_serials.append(device.serial)

            # Thêm các LDPlayer ports thường dùng
            for port in range(5555, 5586):
                ldplayer_serial = f"127.0.0.1:{port}"
                try:
                    # Thử kết nối để kiểm tra
                    test_device = adb.device(ldplayer_serial)
                    if test_device and ldplayer_serial not in device_serials:
                        device_serials.append(ldplayer_serial)
                except:
                    pass

            return device_serials
        except Exception as e:
            print(f"Lỗi quét thiết bị: {e}")
            return []

    def add_device(self, device_serial: str) -> bool:
        """Thêm thiết bị vào danh sách quản lý"""
        with self.lock:
            if device_serial not in self.devices:
                controller = ADBController(device_serial)
                if controller.device:
                    self.devices[device_serial] = controller
                    self.device_status[device_serial] = True
                    self.active_devices.append(device_serial)

                    # Đăng ký với synchronizer
                    self.synchronizer.register_device(device_serial, controller)

                    print(f"Đã thêm thiết bị: {device_serial}")
                    return True
                else:
                    print(f"Không thể kết nối với thiết bị: {device_serial}")
                    return False
            else:
                print(f"Thiết bị {device_serial} đã tồn tại")
                return True

    def remove_device(self, device_serial: str) -> bool:
        """Xóa thiết bị khỏi danh sách quản lý"""
        with self.lock:
            if device_serial in self.devices:
                # Hủy đăng ký với synchronizer
                self.synchronizer.unregister_device(device_serial)

                del self.devices[device_serial]
                del self.device_status[device_serial]
                if device_serial in self.active_devices:
                    self.active_devices.remove(device_serial)
                print(f"Đã xóa thiết bị: {device_serial}")
                return True
            return False

    def get_device(self, device_serial: str) -> Optional[ADBController]:
        """Lấy controller của thiết bị cụ thể"""
        return self.devices.get(device_serial)

    def get_all_devices(self) -> Dict[str, ADBController]:
        """Lấy tất cả thiết bị đang quản lý"""
        return self.devices.copy()

    def get_active_devices(self) -> List[str]:
        """Lấy danh sách thiết bị đang hoạt động"""
        return self.active_devices.copy()

    def check_device_status(self, device_serial: str) -> bool:
        """Kiểm tra trạng thái kết nối của thiết bị"""
        if device_serial in self.devices:
            try:
                controller = self.devices[device_serial]
                # Thử chụp màn hình để kiểm tra kết nối
                screenshot = controller.screenshot()
                status = screenshot is not None
                self.device_status[device_serial] = status
                return status
            except:
                self.device_status[device_serial] = False
                return False
        return False

    def refresh_all_devices(self):
        """Làm mới trạng thái tất cả thiết bị"""
        with self.lock:
            for device_serial in list(self.devices.keys()):
                if not self.check_device_status(device_serial):
                    print(f"Thiết bị {device_serial} mất kết nối")
                    if device_serial in self.active_devices:
                        self.active_devices.remove(device_serial)

    def execute_on_device(self, device_serial: str, action: str, *args, **kwargs):
        """Thực hiện hành động trên thiết bị cụ thể"""
        controller = self.get_device(device_serial)
        if controller and hasattr(controller, action):
            try:
                method = getattr(controller, action)
                return method(*args, **kwargs)
            except Exception as e:
                print(f"Lỗi thực hiện {action} trên {device_serial}: {e}")
                return None
        return None

    def execute_on_all_devices(self, action: str, *args, **kwargs) -> Dict[str, any]:
        """Thực hiện hành động trên tất cả thiết bị đang hoạt động"""
        results = {}
        threads = []

        def execute_single(device_serial):
            result = self.execute_on_device(device_serial, action, *args, **kwargs)
            results[device_serial] = result

        # Tạo thread cho mỗi thiết bị
        for device_serial in self.active_devices:
            thread = threading.Thread(target=execute_single, args=(device_serial,))
            threads.append(thread)
            thread.start()

        # Đợi tất cả thread hoàn thành
        for thread in threads:
            thread.join()

        return results

    def take_screenshots_all(self) -> Dict[str, np.ndarray]:
        """Chụp màn hình tất cả thiết bị"""
        return self.execute_on_all_devices('screenshot')

    def tap_all_devices(self, x: int, y: int):
        """Click vào tọa độ trên tất cả thiết bị"""
        return self.execute_on_all_devices('tap', x, y)

    def swipe_all_devices(self, x1: int, y1: int, x2: int, y2: int, duration: int = 300):
        """Vuốt trên tất cả thiết bị"""
        return self.execute_on_all_devices('swipe', x1, y1, x2, y2, duration)

    def input_text_all_devices(self, text: str):
        """Nhập text trên tất cả thiết bị"""
        return self.execute_on_all_devices('input_text', text)

    def key_event_all_devices(self, keycode: int):
        """Gửi phím trên tất cả thiết bị"""
        return self.execute_on_all_devices('key_event', keycode)

    def _setup_sync_handlers(self):
        """Thiết lập các handler cho synchronizer"""
        self.synchronizer.register_action_handler('tap', default_tap_handler)
        self.synchronizer.register_action_handler('swipe', default_swipe_handler)
        self.synchronizer.register_action_handler('input_text', default_input_text_handler)
        self.synchronizer.register_action_handler('key_event', default_key_event_handler)

    def set_sync_mode(self, mode: SyncMode, leader_device: str = None):
        """Thiết lập chế độ đồng bộ"""
        self.synchronizer.set_sync_mode(mode, leader_device)

    def sync_tap(self, x: int, y: int, target_devices: List[str] = None, delay: float = 0):
        """Thực hiện tap đồng bộ"""
        action = SyncAction('tap', {'x': x, 'y': y}, delay)
        self.synchronizer.execute_action(action, target_devices)

    def sync_swipe(self, x1: int, y1: int, x2: int, y2: int, duration: int = 300,
                   target_devices: List[str] = None, delay: float = 0):
        """Thực hiện swipe đồng bộ"""
        action = SyncAction('swipe', {
            'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2, 'duration': duration
        }, delay)
        self.synchronizer.execute_action(action, target_devices)

    def sync_input_text(self, text: str, target_devices: List[str] = None, delay: float = 0):
        """Thực hiện input text đồng bộ"""
        action = SyncAction('input_text', {'text': text}, delay)
        self.synchronizer.execute_action(action, target_devices)

    def sync_key_event(self, keycode: int, target_devices: List[str] = None, delay: float = 0):
        """Thực hiện key event đồng bộ"""
        action = SyncAction('key_event', {'keycode': keycode}, delay)
        self.synchronizer.execute_action(action, target_devices)

    def get_sync_stats(self):
        """Lấy thống kê đồng bộ"""
        return self.synchronizer.get_sync_stats()

    def get_device_sync_status(self):
        """Lấy trạng thái đồng bộ của thiết bị"""
        return self.synchronizer.get_device_status()