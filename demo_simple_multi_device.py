#!/usr/bin/env python3
"""
Demo đơn giản cho tính năng multi-device auto train
"""

import sys
import time
from core.adb_controller import MultiDeviceController
from core.multi_auto_train import MultiDeviceAutoTrain
from core.image_recognition import ImageRecognition


def main():
    print("🚀 DEMO MULTI-DEVICE AUTO TRAIN 🚀")
    print("=" * 50)
    
    try:
        # Khởi tạo các components
        print("1. Khởi tạo components...")
        controller = MultiDeviceController()
        image_rec = ImageRecognition()
        multi_auto_train = MultiDeviceAutoTrain(controller, image_rec)
        
        # Quét thiết bị
        print("\n2. Quét thiết bị có sẵn...")
        devices = controller.scan_devices()
        print(f"Tìm thấy {len(devices)} thiết bị: {devices}")
        
        if not devices:
            print("❌ Không tìm thấy thiết bị nào!")
            return
        
        # Thêm thiết bị (tối đa 3 thiết bị để demo)
        print("\n3. Thêm thiết bị vào quản lý...")
        added_devices = 0
        for device in devices[:3]:  # Chỉ thêm tối đa 3 thiết bị
            success = controller.add_device(device)
            if success:
                added_devices += 1
                print(f"✅ Đã thêm: {device}")
            else:
                print(f"❌ Không thể thêm: {device}")
        
        if added_devices == 0:
            print("❌ Không thể thêm thiết bị nào!")
            return
        
        # Hiển thị thiết bị đang hoạt động
        print(f"\n4. Thiết bị đang hoạt động ({added_devices} thiết bị):")
        active_devices = controller.get_active_devices()
        for i, device in enumerate(active_devices, 1):
            status = controller.check_device_status(device)
            print(f"   {i}. {device}: {'✅ Hoạt động' if status else '❌ Mất kết nối'}")
        
        # Chụp màn hình test
        print("\n5. Test chụp màn hình tất cả thiết bị...")
        screenshots = controller.take_screenshots_all()
        for device, screenshot in screenshots.items():
            if screenshot is not None:
                print(f"   ✅ {device}: Screenshot OK ({screenshot.shape})")
            else:
                print(f"   ❌ {device}: Không thể chụp màn hình")
        
        # Hỏi user có muốn bắt đầu auto train không
        print("\n" + "="*50)
        choice = input("Bạn có muốn bắt đầu auto train trên tất cả thiết bị? (y/n): ").lower()
        
        if choice == 'y':
            print("\n6. Bắt đầu auto train...")
            
            # Hiển thị các bãi train có sẵn
            train_spots = {
                '1': 'bai_1',
                '2': 'bai_2'
            }
            
            print("Chọn bãi train:")
            print("1. Bãi train cấp 1-20")
            print("2. Bãi train cấp 20-40")
            
            spot_choice = input("Nhập lựa chọn (1-2): ").strip()
            train_spot = train_spots.get(spot_choice, 'bai_1')
            
            # Bắt đầu auto train
            success = multi_auto_train.start_auto_train_all(train_spot)
            
            if success:
                print(f"✅ Đã bắt đầu auto train trên {len(active_devices)} thiết bị!")
                print("\nThống kê sẽ được cập nhật mỗi 10 giây...")
                print("Nhấn Ctrl+C để dừng\n")
                
                # Loop hiển thị thống kê
                try:
                    while True:
                        time.sleep(10)
                        
                        # Hiển thị thống kê tổng quan
                        summary = multi_auto_train.get_summary_stats()
                        print(f"\n📊 THỐNG KÊ TỔNG QUAN:")
                        print(f"   Thiết bị đang chạy: {summary['running_devices']}/{summary['total_devices']}")
                        print(f"   Tổng kills: {summary['total_kills']}")
                        print(f"   Tổng deaths: {summary['total_deaths']}")
                        print(f"   K/D ratio: {summary['kill_death_ratio']}")
                        print(f"   Thời gian: {summary['avg_duration']}")
                        
                        # Hiển thị thống kê từng thiết bị
                        print(f"\n📱 CHI TIẾT TỪNG THIẾT BỊ:")
                        all_stats = multi_auto_train.get_all_stats()
                        for device, stats in all_stats.items():
                            status_icon = "🟢" if stats.get('status') == 'running' else "🔴"
                            print(f"   {status_icon} {device}:")
                            print(f"      Status: {stats.get('status', 'unknown')}")
                            print(f"      Kills: {stats.get('kill_count', 0)}")
                            print(f"      Deaths: {stats.get('death_count', 0)}")
                            print(f"      Duration: {stats.get('duration', 'N/A')}")
                            if 'error' in stats:
                                print(f"      ❌ Error: {stats['error']}")
                        
                        print("-" * 50)
                        
                except KeyboardInterrupt:
                    print("\n\n⏹️ Đang dừng auto train...")
                    multi_auto_train.stop_auto_train_all()
                    print("✅ Đã dừng auto train trên tất cả thiết bị")
            
            else:
                print("❌ Không thể bắt đầu auto train!")
        
        else:
            print("\n📱 Demo các tính năng cơ bản...")
            
            # Demo click trên tất cả thiết bị
            print("   Test click tọa độ (500, 300) trên tất cả thiết bị...")
            controller.tap_all_devices(500, 300)
            time.sleep(1)
            
            # Demo swipe trên tất cả thiết bị
            print("   Test swipe trên tất cả thiết bị...")
            controller.swipe_all_devices(100, 100, 900, 100, 500)
            time.sleep(1)
            
            print("✅ Demo hoàn thành!")
    
    except KeyboardInterrupt:
        print("\n\n⏹️ Demo bị dừng bởi người dùng.")
    except Exception as e:
        print(f"\n❌ Lỗi trong quá trình demo: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n👋 Cảm ơn bạn đã sử dụng Multi-Device Auto Tool!")


def test_single_device():
    """Test với một thiết bị duy nhất"""
    print("🧪 TEST SINGLE DEVICE")
    print("=" * 30)
    
    controller = MultiDeviceController()
    devices = controller.scan_devices()
    
    if not devices:
        print("❌ Không tìm thấy thiết bị nào!")
        return
    
    # Thêm thiết bị đầu tiên
    device = devices[0]
    success = controller.add_device(device)
    
    if success:
        print(f"✅ Đã kết nối với: {device}")
        
        # Test screenshot
        screenshots = controller.take_screenshots_all()
        if device in screenshots and screenshots[device] is not None:
            print(f"✅ Screenshot OK: {screenshots[device].shape}")
        else:
            print("❌ Không thể chụp màn hình")
        
        # Test auto train
        image_rec = ImageRecognition()
        multi_auto_train = MultiDeviceAutoTrain(controller, image_rec)
        
        print("Bắt đầu auto train trong 30 giây...")
        multi_auto_train.start_auto_train_all('bai_1')
        
        # Chạy 30 giây
        for i in range(6):
            time.sleep(5)
            stats = multi_auto_train.get_all_stats()
            print(f"Stats sau {(i+1)*5}s: {stats}")
        
        multi_auto_train.stop_auto_train_all()
        print("✅ Test hoàn thành!")
    
    else:
        print(f"❌ Không thể kết nối với: {device}")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_single_device()
    else:
        main()
