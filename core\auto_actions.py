import time
import random
from typing import List, Dict, Tuple, Optional

class AutoActions:
    def __init__(self, adb_controller, image_recognition):
        self.adb = adb_controller
        self.image_rec = image_recognition
        self.running = False
        
    def auto_hunt_boss(self, boss_names: List[str]):
        """Auto săn boss theo danh sách"""
        while self.running:
            screenshot = self.adb.screenshot()
            if screenshot is None:
                continue
            
            # Tìm boss trên b<PERSON>n đồ
            for boss_name in boss_names:
                boss_pos = self.image_rec.find_template(screenshot, f"boss_{boss_name}")
                if boss_pos:
                    print(f"Tìm thấy boss {boss_name}")
                    
                    # Click vào boss
                    self.adb.tap(boss_pos[0], boss_pos[1])
                    time.sleep(1)
                    
                    # Tìm nút tấn công
                    attack_pos = self.image_rec.find_template(screenshot, "attack_button")
                    if attack_pos:
                        self.adb.tap(attack_pos[0], attack_pos[1])
                        
                        # Đ<PERSON><PERSON> đ<PERSON>ong
                        self._wait_combat_finish()
                        
                        # Nhặt đồ
                        self._pick_items()
                        break
            
            time.sleep(2)
    
    def auto_quest(self):
        """Auto làm nhiệm vụ"""
        while self.running:
            screenshot = self.adb.screenshot()
            if screenshot is None:
                continue
            
            # Tìm icon nhiệm vụ
            quest_pos = self.image_rec.find_template(screenshot, "quest_icon")
            if quest_pos:
                self.adb.tap(quest_pos[0], quest_pos[1])
                time.sleep(2)
                
                # Tìm nút nhận/trả nhiệm vụ
                accept_pos = self.image_rec.find_template(screenshot, "accept_quest")
                if accept_pos:
                    self.adb.tap(accept_pos[0], accept_pos[1])
                    time.sleep(1)
                else:
                    # Tìm nút auto di chuyển
                    auto_move = self.image_rec.find_template(screenshot, "auto_move")
                    if auto_move:
                        self.adb.tap(auto_move[0], auto_move[1])
            
            time.sleep(3)
    
    def auto_use_skills(self, skill_slots: List[int], cooldowns: List[int]):
        """Auto sử dụng skill theo thứ tự và cooldown"""
        last_use_time = {slot: 0 for slot in skill_slots}
        
        while self.running:
            screenshot = self.adb.screenshot()
            if screenshot is None:
                continue
            
            # Kiểm tra đang trong combat
            if self._is_in_combat(screenshot):
                current_time = time.time()
                
                for i, slot in enumerate(skill_slots):
                    # Kiểm tra cooldown
                    if current_time - last_use_time[slot] >= cooldowns[i]:
                        # Sử dụng skill
                        skill_pos = self._get_skill_position(slot)
                        if skill_pos:
                            self.adb.tap(skill_pos[0], skill_pos[1])
                            last_use_time[slot] = current_time
                            time.sleep(0.5)
            
            time.sleep(0.5)
    
    def auto_pick_items(self):
        """Auto nhặt đồ rơi"""
        while self.running:
            screenshot = self.adb.screenshot()
            if screenshot is None:
                continue
            
            # Tìm tất cả item trên màn hình
            items = self.image_rec.find_all_templates(screenshot, "item_drop")
            
            for item_pos in items:
                # Click vào item
                self.adb.tap(item_pos[0], item_pos[1])
                time.sleep(0.3)
            
            time.sleep(1)
    
    def auto_hp_mp(self, hp_percent: int = 50, mp_percent: int = 30):
        """Auto sử dụng HP/MP khi dưới ngưỡng"""
        while self.running:
            screenshot = self.adb.screenshot()
            if screenshot is None:
                continue
            
            # Kiểm tra HP
            current_hp = self._get_hp_percent(screenshot)
            if current_hp and current_hp < hp_percent:
                # Sử dụng HP potion
                hp_pos = self.image_rec.find_template(screenshot, "hp_potion")
                if hp_pos:
                    self.adb.tap(hp_pos[0], hp_pos[1])
                    time.sleep(0.5)
            
            # Kiểm tra MP
            current_mp = self._get_mp_percent(screenshot)
            if current_mp and current_mp < mp_percent:
                # Sử dụng MP potion
                mp_pos = self.image_rec.find_template(screenshot, "mp_potion")
                if mp_pos:
                    self.adb.tap(mp_pos[0], mp_pos[1])
                    time.sleep(0.5)
            
            time.sleep(1)
    
    def _wait_combat_finish(self, timeout: int = 60):
        """Đợi combat kết thúc"""
        start_time = time.time()
        
        while time.time() - start_time < timeout and self.running:
            screenshot = self.adb.screenshot()
            if screenshot is None:
                continue
            
            # Kiểm tra xem combat đã kết thúc chưa
            if not self._is_in_combat(screenshot):
                return True
            
            # Tiếp tục sử dụng skill trong combat
            skill_pos = self._get_skill_position(1)
            if skill_pos:
                self.adb.tap(skill_pos[0], skill_pos[1])
            
            time.sleep(1)
        
        return False
    
    def _pick_items(self):
        """Nhặt đồ sau khi đánh xong"""
        for _ in range(5):  # Thử nhặt 5 lần
            screenshot = self.adb.screenshot()
            if screenshot is None:
                continue
            
            items = self.image_rec.find_all_templates(screenshot, "item_drop")
            if not items:
                break
            
            for item_pos in items[:3]:  # Nhặt tối đa 3 item mỗi lần
                self.adb.tap(item_pos[0], item_pos[1])
                time.sleep(0.3)
            
            time.sleep(0.5)
    
    def _is_in_combat(self, screenshot) -> bool:
        """Kiểm tra đang trong combat"""
        # Tìm thanh máu của quái
        monster_hp = self.image_rec.find_template(screenshot, "monster_hp_bar")
        return monster_hp is not None
    
    def _get_skill_position(self, slot: int) -> Optional[Tuple[int, int]]:
        """Lấy vị trí của skill slot (1-8)"""
        # Giả sử skill bar ở dưới màn hình
        # Cần điều chỉnh theo game cụ thể
        base_x = 100
        base_y = 600
        skill_width = 80
        
        x = base_x + (slot - 1) * skill_width
        y = base_y
        
        return (x, y)
    
    def _get_hp_percent(self, screenshot) -> Optional[int]:
        """Lấy % HP hiện tại"""
        # Cần implement dựa trên UI game
        # Có thể dùng OCR hoặc phân tích màu thanh máu
        return None
    
    def _get_mp_percent(self, screenshot) -> Optional[int]:
        """Lấy % MP hiện tại"""
        # Cần implement dựa trên UI game
        return None
    
    def move_to_position(self, target_x: int, target_y: int):
        """Di chuyển đến vị trí chỉ định trên bản đồ"""
        # Click vào mini map
        self.adb.tap(target_x, target_y)
        time.sleep(1)
        
        # Tìm nút auto di chuyển
        screenshot = self.adb.screenshot()
        auto_move = self.image_rec.find_template(screenshot, "auto_move_button")
        if auto_move:
            self.adb.tap(auto_move[0], auto_move[1])
    
    def random_movement(self, area: Tuple[int, int, int, int]):
        """Di chuyển ngẫu nhiên trong khu vực"""
        x1, y1, x2, y2 = area
        
        while self.running:
            # Random vị trí trong khu vực
            target_x = random.randint(x1, x2)
            target_y = random.randint(y1, y2)
            
            self.move_to_position(target_x, target_y)
            
            # Đợi di chuyển xong (10-30s)
            wait_time = random.randint(10, 30)
            time.sleep(wait_time)
    
    def start(self):
        """Bắt đầu auto"""
        self.running = True
    
    def stop(self):
        """Dừng auto"""
        self.running = False