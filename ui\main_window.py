from PyQt5.QtWidgets import (QMain<PERSON><PERSON>ow, QW<PERSON>t, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QTextEdit, QGroupBox,
                            QCheckBox, QSpinBox, QComboBox, QTableWidget,
                            QTableWidgetItem, QHeaderView, QFileDialog,
                            QTabWidget, QListWidget, QListWidgetItem,
                            QSplitter, QFrame)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QPixmap, QImage
import cv2
import numpy as np
import time
from core.adb_controller import MultiDeviceController
from core.multi_auto_train import MultiDeviceAutoTrain

class AutoThread(QThread):
    log_signal = pyqtSignal(str)
    screenshot_signal = pyqtSignal(np.ndarray)

    def __init__(self, adb_controller, image_recognition):
        super().__init__()
        self.adb = adb_controller
        self.image_rec = image_recognition
        self.running = False
        self.tasks = []

    def add_task(self, task):
        self.tasks.append(task)

    def clear_tasks(self):
        self.tasks = []

    def run(self):
        self.running = True
        while self.running:
            for task in self.tasks:
                if not self.running:
                    break

                # Chụp màn hình
                screenshot = self.adb.screenshot()
                if screenshot is not None:
                    self.screenshot_signal.emit(screenshot)

                    # Thực hiện task
                    if task['type'] == 'click_image':
                        pos = self.image_rec.find_template(screenshot, task['template'])
                        if pos:
                            self.adb.tap(pos[0], pos[1])
                            self.log_signal.emit(f"Đã click vào {task['template']} tại {pos}")
                        else:
                            self.log_signal.emit(f"Không tìm thấy {task['template']}")

                    elif task['type'] == 'wait_and_click':
                        # Đợi cho đến khi tìm thấy
                        max_wait = task.get('timeout', 10)
                        waited = 0
                        while waited < max_wait and self.running:
                            screenshot = self.adb.screenshot()
                            pos = self.image_rec.find_template(screenshot, task['template'])
                            if pos:
                                self.adb.tap(pos[0], pos[1])
                                self.log_signal.emit(f"Đã click vào {task['template']}")
                                break
                            self.msleep(1000)
                            waited += 1

                # Delay giữa các action
                self.msleep(task.get('delay', 1000))

    def stop(self):
        self.running = False


# Đã loại bỏ MultiDeviceAutoThread - không cần thiết

# Đã loại bỏ AutoTrainThread - không cần thiết

class MainWindow(QMainWindow):
    def __init__(self, adb_controller, image_recognition):
        super().__init__()
        self.adb = adb_controller
        self.image_rec = image_recognition

        # Khởi tạo MultiDeviceController
        self.multi_device_controller = MultiDeviceController()
        self.current_device = None
        self.device_screenshots = {}

        # Chỉ giữ lại 2 loại cần thiết
        self.auto_thread = AutoThread(adb_controller, image_recognition)
        self.multi_auto_train = MultiDeviceAutoTrain(self.multi_device_controller, image_recognition)
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        self.setWindowTitle("Auto Tool - Võ Lâm Truyền Kỳ")
        self.setGeometry(100, 100, 1200, 800)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QHBoxLayout(central_widget)
        
        # Left panel - Controls with Tabs
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # Multi-Device Connection group
        conn_group = QGroupBox("Quản lý thiết bị")
        conn_layout = QVBoxLayout()

        # Device scanning
        scan_layout = QHBoxLayout()
        self.scan_btn = QPushButton("Quét thiết bị")
        self.scan_btn.clicked.connect(self.scan_devices)
        scan_layout.addWidget(self.scan_btn)

        self.refresh_btn = QPushButton("Làm mới")
        self.refresh_btn.clicked.connect(self.refresh_devices)
        scan_layout.addWidget(self.refresh_btn)
        conn_layout.addLayout(scan_layout)

        # Available devices
        conn_layout.addWidget(QLabel("Thiết bị có sẵn:"))
        self.available_devices_combo = QComboBox()
        conn_layout.addWidget(self.available_devices_combo)

        # Add/Remove device buttons
        device_btn_layout = QHBoxLayout()
        self.add_device_btn = QPushButton("Thêm thiết bị")
        self.add_device_btn.clicked.connect(self.add_device)
        device_btn_layout.addWidget(self.add_device_btn)

        self.remove_device_btn = QPushButton("Xóa thiết bị")
        self.remove_device_btn.clicked.connect(self.remove_device)
        device_btn_layout.addWidget(self.remove_device_btn)
        conn_layout.addLayout(device_btn_layout)

        # Connected devices list
        conn_layout.addWidget(QLabel("Thiết bị đã kết nối:"))
        self.connected_devices_list = QListWidget()
        self.connected_devices_list.itemClicked.connect(self.select_device)
        conn_layout.addWidget(self.connected_devices_list)

        # Current device status
        self.status_label = QLabel("Chưa chọn thiết bị")
        conn_layout.addWidget(self.status_label)

        # Multi-device actions
        multi_action_layout = QHBoxLayout()
        self.screenshot_all_btn = QPushButton("Chụp tất cả")
        self.screenshot_all_btn.clicked.connect(self.screenshot_all_devices)
        multi_action_layout.addWidget(self.screenshot_all_btn)

        conn_layout.addLayout(multi_action_layout)

        conn_group.setLayout(conn_layout)
        left_layout.addWidget(conn_group)
        
        # Tab widget for different auto features
        self.tab_widget = QTabWidget()
        
        # Tab 1: Auto Features
        auto_tab = QWidget()
        auto_tab_layout = QVBoxLayout()
        
        # Auto features group
        auto_group = QGroupBox("Chức năng Auto")
        auto_layout = QVBoxLayout()
        
        # Auto hunt
        self.auto_hunt_cb = QCheckBox("Auto săn boss")
        auto_layout.addWidget(self.auto_hunt_cb)
        
        # Auto quest
        self.auto_quest_cb = QCheckBox("Auto nhiệm vụ")
        auto_layout.addWidget(self.auto_quest_cb)
        
        # Auto pick items
        self.auto_pick_cb = QCheckBox("Auto nhặt đồ")
        auto_layout.addWidget(self.auto_pick_cb)

        # Auto train
        self.auto_train_cb = QCheckBox("Auto train")
        auto_layout.addWidget(self.auto_train_cb)
        
        # Auto use skills
        self.auto_skill_cb = QCheckBox("Auto sử dụng skill")
        auto_layout.addWidget(self.auto_skill_cb)
        
        auto_group.setLayout(auto_layout)
        auto_tab_layout.addWidget(auto_group)
        
        # Control buttons
        control_group = QGroupBox("Điều khiển")
        control_layout = QVBoxLayout()
        
        self.start_btn = QPushButton("Bắt đầu Auto")
        self.start_btn.clicked.connect(self.start_auto)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("Dừng Auto")
        self.stop_btn.clicked.connect(self.stop_auto)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        control_group.setLayout(control_layout)
        auto_tab_layout.addWidget(control_group)
        auto_tab_layout.addStretch()
        auto_tab.setLayout(auto_tab_layout)
        
        # Tab 2: Auto Train
        train_tab = QWidget()
        train_tab_layout = QVBoxLayout()
        
        # Train settings
        train_settings_group = QGroupBox("Cài đặt Train")
        train_settings_layout = QVBoxLayout()
        
        # Chọn bãi train
        train_settings_layout.addWidget(QLabel("Chọn bãi train:"))
        self.train_spot_combo = QComboBox()
        self.train_spot_combo.addItems([
            "bai_1 - Bãi train cấp 1-20",
            "bai_2 - Bãi train cấp 20-40"
        ])
        train_settings_layout.addWidget(self.train_spot_combo)
        
        # Auto return when dead
        self.auto_return_cb = QCheckBox("Tự động quay lại bãi khi chết")
        self.auto_return_cb.setChecked(True)
        train_settings_layout.addWidget(self.auto_return_cb)
        
        # HP/MP settings
        hp_layout = QHBoxLayout()
        hp_layout.addWidget(QLabel("Dùng HP khi dưới:"))
        self.hp_threshold_spin = QSpinBox()
        self.hp_threshold_spin.setRange(10, 90)
        self.hp_threshold_spin.setValue(50)
        self.hp_threshold_spin.setSuffix("%")
        hp_layout.addWidget(self.hp_threshold_spin)
        train_settings_layout.addLayout(hp_layout)
        
        mp_layout = QHBoxLayout()
        mp_layout.addWidget(QLabel("Dùng MP khi dưới:"))
        self.mp_threshold_spin = QSpinBox()
        self.mp_threshold_spin.setRange(10, 90)
        self.mp_threshold_spin.setValue(30)
        self.mp_threshold_spin.setSuffix("%")
        mp_layout.addWidget(self.mp_threshold_spin)
        train_settings_layout.addLayout(mp_layout)
        
        train_settings_group.setLayout(train_settings_layout)
        train_tab_layout.addWidget(train_settings_group)
        
        # Train control
        train_control_group = QGroupBox("Điều khiển Train")
        train_control_layout = QVBoxLayout()
        
        self.start_train_btn = QPushButton("Bắt đầu Train")
        self.start_train_btn.clicked.connect(self.start_train)
        train_control_layout.addWidget(self.start_train_btn)
        
        self.stop_train_btn = QPushButton("Dừng Train")
        self.stop_train_btn.clicked.connect(self.stop_train)
        self.stop_train_btn.setEnabled(False)
        train_control_layout.addWidget(self.stop_train_btn)
        
        train_control_group.setLayout(train_control_layout)
        train_tab_layout.addWidget(train_control_group)
        
        # Train statistics
        train_stats_group = QGroupBox("Thống kê Train")
        train_stats_layout = QVBoxLayout()
        
        self.train_duration_label = QLabel("Thời gian: 00:00:00")
        train_stats_layout.addWidget(self.train_duration_label)
        
        self.train_kills_label = QLabel("Số quái đã giết: 0")
        train_stats_layout.addWidget(self.train_kills_label)
        
        self.train_deaths_label = QLabel("Số lần chết: 0")
        train_stats_layout.addWidget(self.train_deaths_label)
        
        self.train_kph_label = QLabel("Quái/giờ: 0")
        train_stats_layout.addWidget(self.train_kph_label)
        
        train_stats_group.setLayout(train_stats_layout)
        train_tab_layout.addWidget(train_stats_group)
        
        train_tab_layout.addStretch()
        train_tab.setLayout(train_tab_layout)
        
        # Tab 3: Templates
        template_tab = QWidget()
        template_tab_layout = QVBoxLayout()
        
        # Template management
        template_group = QGroupBox("Quản lý Template")
        template_layout = QVBoxLayout()
        
        self.load_template_btn = QPushButton("Load Template")
        self.load_template_btn.clicked.connect(self.load_template)
        template_layout.addWidget(self.load_template_btn)
        
        self.template_list = QTableWidget(0, 2)
        self.template_list.setHorizontalHeaderLabels(["Tên", "Đường dẫn"])
        self.template_list.horizontalHeader().setStretchLastSection(True)
        template_layout.addWidget(self.template_list)
        
        template_group.setLayout(template_layout)
        template_tab_layout.addWidget(template_group)
        template_tab.setLayout(template_tab_layout)
        
        # Add tabs to tab widget
        self.tab_widget.addTab(auto_tab, "Auto Cơ bản")
        self.tab_widget.addTab(train_tab, "Auto Train")
        self.tab_widget.addTab(template_tab, "Templates")
        
        left_layout.addWidget(self.tab_widget)
        
        # Screenshot button
        self.screenshot_btn = QPushButton("Chụp màn hình")
        self.screenshot_btn.clicked.connect(self.take_screenshot)
        left_layout.addWidget(self.screenshot_btn)
        
        main_layout.addWidget(left_panel, 1)
        
        # Right panel - Display and logs
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Screenshot display
        self.screenshot_label = QLabel()
        self.screenshot_label.setMinimumSize(480, 270)
        self.screenshot_label.setStyleSheet("border: 1px solid black")
        self.screenshot_label.setScaledContents(True)
        right_layout.addWidget(QLabel("Màn hình:"))
        right_layout.addWidget(self.screenshot_label)
        
        # Log display
        right_layout.addWidget(QLabel("Nhật ký:"))
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        right_layout.addWidget(self.log_text)
        
        main_layout.addWidget(right_panel, 2)
        
        # Timer for periodic screenshot
        self.screenshot_timer = QTimer()
        self.screenshot_timer.timeout.connect(self.update_screenshot)

        # Timer for auto train stats update
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_auto_train_stats)
        self.stats_timer.start(5000)  # Update every 5 seconds

        # Khởi tạo quét thiết bị
        self.scan_devices()
    
    def setup_connections(self):
        self.auto_thread.log_signal.connect(self.add_log)
        self.auto_thread.screenshot_signal.connect(self.display_screenshot)

    
    def scan_devices(self):
        """Quét tìm thiết bị có sẵn"""
        devices = self.multi_device_controller.scan_devices()
        self.available_devices_combo.clear()
        self.available_devices_combo.addItems(devices)
        self.add_log(f"Tìm thấy {len(devices)} thiết bị")

    def refresh_devices(self):
        """Làm mới trạng thái thiết bị"""
        self.multi_device_controller.refresh_all_devices()
        self.update_connected_devices_list()
        self.add_log("Đã làm mới trạng thái thiết bị")

    def add_device(self):
        """Thêm thiết bị vào danh sách quản lý"""
        device_serial = self.available_devices_combo.currentText()
        if device_serial:
            if self.multi_device_controller.add_device(device_serial):
                self.update_connected_devices_list()
                self.add_log(f"Đã thêm thiết bị: {device_serial}")
            else:
                self.add_log(f"Không thể thêm thiết bị: {device_serial}")

    def remove_device(self):
        """Xóa thiết bị khỏi danh sách quản lý"""
        current_item = self.connected_devices_list.currentItem()
        if current_item:
            device_serial = current_item.text().split(" - ")[0]
            if self.multi_device_controller.remove_device(device_serial):
                self.update_connected_devices_list()
                self.add_log(f"Đã xóa thiết bị: {device_serial}")
                if self.current_device == device_serial:
                    self.current_device = None
                    self.status_label.setText("Chưa chọn thiết bị")

    def select_device(self, item):
        """Chọn thiết bị hiện tại"""
        device_serial = item.text().split(" - ")[0]
        self.current_device = device_serial
        controller = self.multi_device_controller.get_device(device_serial)
        if controller:
            self.adb = controller  # Cập nhật controller hiện tại
            self.status_label.setText(f"Đã chọn: {device_serial}")
            self.status_label.setStyleSheet("color: green")
            self.screenshot_timer.start(1000)
        else:
            self.status_label.setText("Thiết bị không khả dụng")
            self.status_label.setStyleSheet("color: red")

    def update_connected_devices_list(self):
        """Cập nhật danh sách thiết bị đã kết nối"""
        self.connected_devices_list.clear()
        devices = self.multi_device_controller.get_all_devices()
        for device_serial, controller in devices.items():
            status = "Hoạt động" if self.multi_device_controller.check_device_status(device_serial) else "Mất kết nối"
            item_text = f"{device_serial} - {status}"
            item = QListWidgetItem(item_text)
            if status == "Hoạt động":
                item.setBackground(Qt.green)
            else:
                item.setBackground(Qt.red)
            self.connected_devices_list.addItem(item)

    def screenshot_all_devices(self):
        """Chụp màn hình tất cả thiết bị"""
        screenshots = self.multi_device_controller.take_screenshots_all()
        self.device_screenshots = screenshots
        self.add_log(f"Đã chụp màn hình {len(screenshots)} thiết bị")

        # Hiển thị screenshot của thiết bị đầu tiên (nếu có)
        if screenshots:
            first_device = list(screenshots.keys())[0]
            if screenshots[first_device] is not None:
                self.display_screenshot(screenshots[first_device])



    def update_auto_train_stats(self):
        """Cập nhật thống kê auto train"""
        if self.multi_auto_train.is_running():
            summary_stats = self.multi_auto_train.get_summary_stats()
            stats_text = (
                f"Thiết bị: {summary_stats['running_devices']}/{summary_stats['total_devices']} | "
                f"Kills: {summary_stats['total_kills']} | "
                f"Deaths: {summary_stats['total_deaths']} | "
                f"K/D: {summary_stats['kill_death_ratio']}"
            )

            # Cập nhật status label hoặc tạo label mới nếu cần
            if hasattr(self, 'auto_stats_label'):
                self.auto_stats_label.setText(f"Auto Stats: {stats_text}")
            else:
                # Tạo label mới nếu chưa có
                self.auto_stats_label = QLabel(f"Auto Stats: {stats_text}")
                # Thêm vào layout nếu cần
        else:
            if hasattr(self, 'auto_stats_label'):
                self.auto_stats_label.setText("Auto Stats: Không có thiết bị nào đang chạy")
    
    def start_auto(self):
        # Kiểm tra có thiết bị nào đang hoạt động không
        active_devices = self.multi_device_controller.get_active_devices()

        if not active_devices:
            self.add_log("Không có thiết bị nào đang hoạt động!")
            return

        # Kiểm tra xem có chọn auto train không
        if self.auto_train_cb.isChecked():
            # Auto train - sử dụng MultiDeviceAutoTrain cho cả 1 và nhiều thiết bị
            success = self.multi_auto_train.start_auto_train_all('bai_1')
            if success:
                self.add_log(f"Đã bắt đầu auto train trên {len(active_devices)} thiết bị")
            else:
                self.add_log("Không thể bắt đầu auto train")
                return
        else:
            # Auto thường - chỉ hoạt động trên thiết bị hiện tại
            if self.current_device:
                self.adb = self.multi_device_controller.get_device(self.current_device)

                # Clear old tasks
                self.auto_thread.clear_tasks()

                # Add tasks based on selected features
                if self.auto_hunt_cb.isChecked():
                    self.auto_thread.add_task({
                        'type': 'click_image',
                        'template': 'boss_icon',
                        'delay': 2000
                    })

                if self.auto_quest_cb.isChecked():
                    self.auto_thread.add_task({
                        'type': 'wait_and_click',
                        'template': 'quest_icon',
                        'timeout': 10
                    })

                # Start thread
                self.auto_thread.start()
                self.add_log(f"Đã bắt đầu auto trên thiết bị: {self.current_device}")
            else:
                self.add_log("Vui lòng chọn một thiết bị trước!")
                return

        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
    
    def stop_auto(self):
        # Dừng auto thread
        if self.auto_thread.isRunning():
            self.auto_thread.stop()
            self.auto_thread.wait()

        # Dừng multi auto train
        if self.multi_auto_train.is_running():
            self.multi_auto_train.stop_auto_train_all()

        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.add_log("Đã dừng auto")
    
    def take_screenshot(self):
        screenshot = self.adb.screenshot()
        if screenshot is not None:
            self.display_screenshot(screenshot)
            # Save screenshot
            cv2.imwrite("screenshot.png", screenshot)
            self.add_log("Đã chụp màn hình")
    
    def update_screenshot(self):
        if not self.auto_thread.isRunning():
            screenshot = self.adb.screenshot()
            if screenshot is not None:
                self.display_screenshot(screenshot)
    
    def display_screenshot(self, screenshot):
        # Convert numpy array to QPixmap
        height, width, channel = screenshot.shape
        bytes_per_line = 3 * width
        
        # Convert BGR to RGB
        rgb_image = cv2.cvtColor(screenshot, cv2.COLOR_BGR2RGB)
        
        q_image = QImage(rgb_image.data, width, height, 
                        bytes_per_line, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(q_image)
        
        # Scale to fit label
        scaled_pixmap = pixmap.scaled(self.screenshot_label.size(), 
                                     Qt.KeepAspectRatio, 
                                     Qt.SmoothTransformation)
        self.screenshot_label.setPixmap(scaled_pixmap)
    
    def load_template(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Chọn template", "", "Image Files (*.png *.jpg *.jpeg)")
        
        if file_path:
            template_name = file_path.split('/')[-1].split('.')[0]
            self.image_rec.load_template(template_name, file_path)
            
            # Add to table
            row = self.template_list.rowCount()
            self.template_list.insertRow(row)
            self.template_list.setItem(row, 0, QTableWidgetItem(template_name))
            self.template_list.setItem(row, 1, QTableWidgetItem(file_path))
            
            self.add_log(f"Đã load template: {template_name}")
    
    def add_log(self, message):
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
    
    def start_train(self):
        """Bắt đầu auto train"""
        spot_text = self.train_spot_combo.currentText()
        spot_key = spot_text.split(" - ")[0]

        # Sử dụng multi_auto_train thay vì auto_train_thread
        active_devices = self.multi_device_controller.get_active_devices()
        if not active_devices:
            self.add_log("Không có thiết bị nào đang hoạt động!")
            return

        success = self.multi_auto_train.start_auto_train_all(spot_key)
        if success:
            self.start_train_btn.setEnabled(False)
            self.stop_train_btn.setEnabled(True)
            self.add_log(f"Bắt đầu train tại: {spot_text} trên {len(active_devices)} thiết bị")
        else:
            self.add_log("Không thể bắt đầu auto train")

    def stop_train(self):
        """Dừng auto train"""
        self.multi_auto_train.stop_auto_train_all()

        self.start_train_btn.setEnabled(True)
        self.stop_train_btn.setEnabled(False)
        self.add_log("Đã dừng train")
    
    def update_train_stats(self, stats):
        """Cập nhật thống kê train"""
        if 'duration' in stats:
            self.train_duration_label.setText(f"Thời gian: {stats['duration']}")
        if 'kill_count' in stats:
            self.train_kills_label.setText(f"Số quái đã giết: {stats['kill_count']}")
        if 'death_count' in stats:
            self.train_deaths_label.setText(f"Số lần chết: {stats['death_count']}")
        if 'kills_per_hour' in stats:
            self.train_kph_label.setText(f"Quái/giờ: {stats['kills_per_hour']}")
    
    def closeEvent(self, event):
        self.stop_auto()
        self.stop_train()
        self.screenshot_timer.stop()
        event.accept()