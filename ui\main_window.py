from PyQt5.QtWidgets import (QMain<PERSON><PERSON>ow, <PERSON>Widget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QTextEdit, QGroupBox,
                            QCheckBox, QSpinBox, QComboBox, QTableWidget,
                            QTableWidgetItem, QHeaderView, QFileDialog,
                            QTabWidget)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QPixmap, QImage
import cv2
import numpy as np
from core.auto_train import AutoTrain

class AutoThread(QThread):
    log_signal = pyqtSignal(str)
    screenshot_signal = pyqtSignal(np.ndarray)
    
    def __init__(self, adb_controller, image_recognition):
        super().__init__()
        self.adb = adb_controller
        self.image_rec = image_recognition
        self.running = False
        self.tasks = []
    
    def add_task(self, task):
        self.tasks.append(task)
    
    def clear_tasks(self):
        self.tasks = []
    
    def run(self):
        self.running = True
        while self.running:
            for task in self.tasks:
                if not self.running:
                    break
                
                # Chụp màn hình
                screenshot = self.adb.screenshot()
                if screenshot is not None:
                    self.screenshot_signal.emit(screenshot)
                    
                    # Thực hiện task
                    if task['type'] == 'click_image':
                        pos = self.image_rec.find_template(screenshot, task['template'])
                        if pos:
                            self.adb.tap(pos[0], pos[1])
                            self.log_signal.emit(f"Đã click vào {task['template']} tại {pos}")
                        else:
                            self.log_signal.emit(f"Không tìm thấy {task['template']}")
                    
                    elif task['type'] == 'wait_and_click':
                        # Đợi cho đến khi tìm thấy
                        max_wait = task.get('timeout', 10)
                        waited = 0
                        while waited < max_wait and self.running:
                            screenshot = self.adb.screenshot()
                            pos = self.image_rec.find_template(screenshot, task['template'])
                            if pos:
                                self.adb.tap(pos[0], pos[1])
                                self.log_signal.emit(f"Đã click vào {task['template']}")
                                break
                            self.msleep(1000)
                            waited += 1
                
                # Delay giữa các action
                self.msleep(task.get('delay', 1000))
    
    def stop(self):
        self.running = False

class AutoTrainThread(QThread):
    log_signal = pyqtSignal(str)
    stats_signal = pyqtSignal(dict)
    
    def __init__(self, auto_train):
        super().__init__()
        self.auto_train = auto_train
        self.train_spot = None
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.emit_stats)
        
    def set_train_spot(self, spot_key):
        self.train_spot = spot_key
    
    def run(self):
        self.stats_timer.start(5000)  # Update stats every 5s
        self.auto_train.start_train(self.train_spot)
    
    def emit_stats(self):
        stats = self.auto_train.get_train_stats()
        self.stats_signal.emit(stats)
    
    def stop(self):
        self.stats_timer.stop()
        self.auto_train.stop()

class MainWindow(QMainWindow):
    def __init__(self, adb_controller, image_recognition):
        super().__init__()
        self.adb = adb_controller
        self.image_rec = image_recognition
        self.auto_thread = AutoThread(adb_controller, image_recognition)
        self.auto_train = AutoTrain(adb_controller, image_recognition)
        self.auto_train_thread = AutoTrainThread(self.auto_train)
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        self.setWindowTitle("Auto Tool - Võ Lâm Truyền Kỳ")
        self.setGeometry(100, 100, 1200, 800)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QHBoxLayout(central_widget)
        
        # Left panel - Controls with Tabs
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # Connection group
        conn_group = QGroupBox("Kết nối")
        conn_layout = QVBoxLayout()
        
        self.device_combo = QComboBox()
        self.device_combo.addItems(["emulator-5554", "127.0.0.1:5555"])
        conn_layout.addWidget(QLabel("Thiết bị:"))
        conn_layout.addWidget(self.device_combo)
        
        self.connect_btn = QPushButton("Kết nối")
        self.connect_btn.clicked.connect(self.connect_device)
        conn_layout.addWidget(self.connect_btn)
        
        self.status_label = QLabel("Chưa kết nối")
        conn_layout.addWidget(self.status_label)
        
        conn_group.setLayout(conn_layout)
        left_layout.addWidget(conn_group)
        
        # Tab widget for different auto features
        self.tab_widget = QTabWidget()
        
        # Tab 1: Auto Features
        auto_tab = QWidget()
        auto_tab_layout = QVBoxLayout()
        
        # Auto features group
        auto_group = QGroupBox("Chức năng Auto")
        auto_layout = QVBoxLayout()
        
        # Auto hunt
        self.auto_hunt_cb = QCheckBox("Auto săn boss")
        auto_layout.addWidget(self.auto_hunt_cb)
        
        # Auto quest
        self.auto_quest_cb = QCheckBox("Auto nhiệm vụ")
        auto_layout.addWidget(self.auto_quest_cb)
        
        # Auto pick items
        self.auto_pick_cb = QCheckBox("Auto nhặt đồ")
        auto_layout.addWidget(self.auto_pick_cb)
        
        # Auto use skills
        self.auto_skill_cb = QCheckBox("Auto sử dụng skill")
        auto_layout.addWidget(self.auto_skill_cb)
        
        auto_group.setLayout(auto_layout)
        auto_tab_layout.addWidget(auto_group)
        
        # Control buttons
        control_group = QGroupBox("Điều khiển")
        control_layout = QVBoxLayout()
        
        self.start_btn = QPushButton("Bắt đầu Auto")
        self.start_btn.clicked.connect(self.start_auto)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("Dừng Auto")
        self.stop_btn.clicked.connect(self.stop_auto)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        control_group.setLayout(control_layout)
        auto_tab_layout.addWidget(control_group)
        auto_tab_layout.addStretch()
        auto_tab.setLayout(auto_tab_layout)
        
        # Tab 2: Auto Train
        train_tab = QWidget()
        train_tab_layout = QVBoxLayout()
        
        # Train settings
        train_settings_group = QGroupBox("Cài đặt Train")
        train_settings_layout = QVBoxLayout()
        
        # Chọn bãi train
        train_settings_layout.addWidget(QLabel("Chọn bãi train:"))
        self.train_spot_combo = QComboBox()
        self.train_spot_combo.addItems([
            "bai_1 - Bãi train cấp 1-20",
            "bai_2 - Bãi train cấp 20-40"
        ])
        train_settings_layout.addWidget(self.train_spot_combo)
        
        # Auto return when dead
        self.auto_return_cb = QCheckBox("Tự động quay lại bãi khi chết")
        self.auto_return_cb.setChecked(True)
        train_settings_layout.addWidget(self.auto_return_cb)
        
        # HP/MP settings
        hp_layout = QHBoxLayout()
        hp_layout.addWidget(QLabel("Dùng HP khi dưới:"))
        self.hp_threshold_spin = QSpinBox()
        self.hp_threshold_spin.setRange(10, 90)
        self.hp_threshold_spin.setValue(50)
        self.hp_threshold_spin.setSuffix("%")
        hp_layout.addWidget(self.hp_threshold_spin)
        train_settings_layout.addLayout(hp_layout)
        
        mp_layout = QHBoxLayout()
        mp_layout.addWidget(QLabel("Dùng MP khi dưới:"))
        self.mp_threshold_spin = QSpinBox()
        self.mp_threshold_spin.setRange(10, 90)
        self.mp_threshold_spin.setValue(30)
        self.mp_threshold_spin.setSuffix("%")
        mp_layout.addWidget(self.mp_threshold_spin)
        train_settings_layout.addLayout(mp_layout)
        
        train_settings_group.setLayout(train_settings_layout)
        train_tab_layout.addWidget(train_settings_group)
        
        # Train control
        train_control_group = QGroupBox("Điều khiển Train")
        train_control_layout = QVBoxLayout()
        
        self.start_train_btn = QPushButton("Bắt đầu Train")
        self.start_train_btn.clicked.connect(self.start_train)
        train_control_layout.addWidget(self.start_train_btn)
        
        self.stop_train_btn = QPushButton("Dừng Train")
        self.stop_train_btn.clicked.connect(self.stop_train)
        self.stop_train_btn.setEnabled(False)
        train_control_layout.addWidget(self.stop_train_btn)
        
        train_control_group.setLayout(train_control_layout)
        train_tab_layout.addWidget(train_control_group)
        
        # Train statistics
        train_stats_group = QGroupBox("Thống kê Train")
        train_stats_layout = QVBoxLayout()
        
        self.train_duration_label = QLabel("Thời gian: 00:00:00")
        train_stats_layout.addWidget(self.train_duration_label)
        
        self.train_kills_label = QLabel("Số quái đã giết: 0")
        train_stats_layout.addWidget(self.train_kills_label)
        
        self.train_deaths_label = QLabel("Số lần chết: 0")
        train_stats_layout.addWidget(self.train_deaths_label)
        
        self.train_kph_label = QLabel("Quái/giờ: 0")
        train_stats_layout.addWidget(self.train_kph_label)
        
        train_stats_group.setLayout(train_stats_layout)
        train_tab_layout.addWidget(train_stats_group)
        
        train_tab_layout.addStretch()
        train_tab.setLayout(train_tab_layout)
        
        # Tab 3: Templates
        template_tab = QWidget()
        template_tab_layout = QVBoxLayout()
        
        # Template management
        template_group = QGroupBox("Quản lý Template")
        template_layout = QVBoxLayout()
        
        self.load_template_btn = QPushButton("Load Template")
        self.load_template_btn.clicked.connect(self.load_template)
        template_layout.addWidget(self.load_template_btn)
        
        self.template_list = QTableWidget(0, 2)
        self.template_list.setHorizontalHeaderLabels(["Tên", "Đường dẫn"])
        self.template_list.horizontalHeader().setStretchLastSection(True)
        template_layout.addWidget(self.template_list)
        
        template_group.setLayout(template_layout)
        template_tab_layout.addWidget(template_group)
        template_tab.setLayout(template_tab_layout)
        
        # Add tabs to tab widget
        self.tab_widget.addTab(auto_tab, "Auto Cơ bản")
        self.tab_widget.addTab(train_tab, "Auto Train")
        self.tab_widget.addTab(template_tab, "Templates")
        
        left_layout.addWidget(self.tab_widget)
        
        # Screenshot button
        self.screenshot_btn = QPushButton("Chụp màn hình")
        self.screenshot_btn.clicked.connect(self.take_screenshot)
        left_layout.addWidget(self.screenshot_btn)
        
        main_layout.addWidget(left_panel, 1)
        
        # Right panel - Display and logs
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Screenshot display
        self.screenshot_label = QLabel()
        self.screenshot_label.setMinimumSize(480, 270)
        self.screenshot_label.setStyleSheet("border: 1px solid black")
        self.screenshot_label.setScaledContents(True)
        right_layout.addWidget(QLabel("Màn hình:"))
        right_layout.addWidget(self.screenshot_label)
        
        # Log display
        right_layout.addWidget(QLabel("Nhật ký:"))
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        right_layout.addWidget(self.log_text)
        
        main_layout.addWidget(right_panel, 2)
        
        # Timer for periodic screenshot
        self.screenshot_timer = QTimer()
        self.screenshot_timer.timeout.connect(self.update_screenshot)
    
    def setup_connections(self):
        self.auto_thread.log_signal.connect(self.add_log)
        self.auto_thread.screenshot_signal.connect(self.display_screenshot)
        self.auto_train_thread.log_signal.connect(self.add_log)
        self.auto_train_thread.stats_signal.connect(self.update_train_stats)
    
    def connect_device(self):
        device_serial = self.device_combo.currentText()
        self.adb.device_serial = device_serial
        if self.adb.connect():
            self.status_label.setText(f"Đã kết nối: {device_serial}")
            self.status_label.setStyleSheet("color: green")
            self.screenshot_timer.start(1000)  # Update mỗi giây
        else:
            self.status_label.setText("Kết nối thất bại")
            self.status_label.setStyleSheet("color: red")
    
    def start_auto(self):
        # Clear old tasks
        self.auto_thread.clear_tasks()
        
        # Add tasks based on selected features
        if self.auto_hunt_cb.isChecked():
            self.auto_thread.add_task({
                'type': 'click_image',
                'template': 'boss_icon',
                'delay': 2000
            })
        
        if self.auto_quest_cb.isChecked():
            self.auto_thread.add_task({
                'type': 'wait_and_click',
                'template': 'quest_icon',
                'timeout': 10
            })
        
        # Start thread
        self.auto_thread.start()
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.add_log("Đã bắt đầu auto")
    
    def stop_auto(self):
        self.auto_thread.stop()
        self.auto_thread.wait()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.add_log("Đã dừng auto")
    
    def take_screenshot(self):
        screenshot = self.adb.screenshot()
        if screenshot is not None:
            self.display_screenshot(screenshot)
            # Save screenshot
            cv2.imwrite("screenshot.png", screenshot)
            self.add_log("Đã chụp màn hình")
    
    def update_screenshot(self):
        if not self.auto_thread.isRunning():
            screenshot = self.adb.screenshot()
            if screenshot is not None:
                self.display_screenshot(screenshot)
    
    def display_screenshot(self, screenshot):
        # Convert numpy array to QPixmap
        height, width, channel = screenshot.shape
        bytes_per_line = 3 * width
        
        # Convert BGR to RGB
        rgb_image = cv2.cvtColor(screenshot, cv2.COLOR_BGR2RGB)
        
        q_image = QImage(rgb_image.data, width, height, 
                        bytes_per_line, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(q_image)
        
        # Scale to fit label
        scaled_pixmap = pixmap.scaled(self.screenshot_label.size(), 
                                     Qt.KeepAspectRatio, 
                                     Qt.SmoothTransformation)
        self.screenshot_label.setPixmap(scaled_pixmap)
    
    def load_template(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Chọn template", "", "Image Files (*.png *.jpg *.jpeg)")
        
        if file_path:
            template_name = file_path.split('/')[-1].split('.')[0]
            self.image_rec.load_template(template_name, file_path)
            
            # Add to table
            row = self.template_list.rowCount()
            self.template_list.insertRow(row)
            self.template_list.setItem(row, 0, QTableWidgetItem(template_name))
            self.template_list.setItem(row, 1, QTableWidgetItem(file_path))
            
            self.add_log(f"Đã load template: {template_name}")
    
    def add_log(self, message):
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
    
    def start_train(self):
        """Bắt đầu auto train"""
        spot_text = self.train_spot_combo.currentText()
        spot_key = spot_text.split(" - ")[0]
        
        self.auto_train_thread.set_train_spot(spot_key)
        self.auto_train_thread.start()
        
        self.start_train_btn.setEnabled(False)
        self.stop_train_btn.setEnabled(True)
        self.add_log(f"Bắt đầu train tại: {spot_text}")
    
    def stop_train(self):
        """Dừng auto train"""
        self.auto_train_thread.stop()
        self.auto_train_thread.wait()
        
        self.start_train_btn.setEnabled(True)
        self.stop_train_btn.setEnabled(False)
        self.add_log("Đã dừng train")
    
    def update_train_stats(self, stats):
        """Cập nhật thống kê train"""
        if 'duration' in stats:
            self.train_duration_label.setText(f"Thời gian: {stats['duration']}")
        if 'kill_count' in stats:
            self.train_kills_label.setText(f"Số quái đã giết: {stats['kill_count']}")
        if 'death_count' in stats:
            self.train_deaths_label.setText(f"Số lần chết: {stats['death_count']}")
        if 'kills_per_hour' in stats:
            self.train_kph_label.setText(f"Quái/giờ: {stats['kills_per_hour']}")
    
    def closeEvent(self, event):
        self.stop_auto()
        self.stop_train()
        self.screenshot_timer.stop()
        event.accept()