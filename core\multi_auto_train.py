import threading
import time
from typing import Dict, List
from .auto_train import AutoTrain


class MultiDeviceAutoTrain:
    """Class để chạy auto train trên nhiều thiết bị cùng lúc"""
    
    def __init__(self, multi_device_controller, image_recognition):
        self.multi_device_controller = multi_device_controller
        self.image_rec = image_recognition
        self.running = False
        self.device_threads: Dict[str, threading.Thread] = {}
        self.device_auto_trains: Dict[str, AutoTrain] = {}
        self.device_stats: Dict[str, Dict] = {}
        
    def start_auto_train_all(self, train_spot_key: str):
        """Bắt đầu auto train trên tất cả thiết bị đang hoạt động"""
        active_devices = self.multi_device_controller.get_active_devices()
        
        if not active_devices:
            print("Không có thiết bị nào đang hoạt động!")
            return False
        
        self.running = True
        print(f"Bắt đầu auto train trên {len(active_devices)} thiết bị...")
        
        # Tạo AutoTrain cho mỗi thiết bị
        for device_serial in active_devices:
            controller = self.multi_device_controller.get_device(device_serial)
            if controller:
                # Tạo AutoTrain instance cho thiết bị này
                auto_train = AutoTrain(controller, self.image_rec)
                self.device_auto_trains[device_serial] = auto_train
                
                # Tạo thread để chạy auto train
                thread = threading.Thread(
                    target=self._run_auto_train_for_device,
                    args=(device_serial, train_spot_key),
                    name=f"AutoTrain-{device_serial}"
                )
                self.device_threads[device_serial] = thread
                thread.start()
                
                print(f"Đã khởi động auto train cho thiết bị: {device_serial}")
        
        return True
    
    def _run_auto_train_for_device(self, device_serial: str, train_spot_key: str):
        """Chạy auto train cho một thiết bị cụ thể"""
        try:
            auto_train = self.device_auto_trains[device_serial]
            print(f"[{device_serial}] Bắt đầu train tại bãi: {train_spot_key}")
            
            # Chạy auto train
            auto_train.start_train(train_spot_key)
            
        except Exception as e:
            print(f"[{device_serial}] Lỗi trong quá trình auto train: {e}")
        finally:
            print(f"[{device_serial}] Đã dừng auto train")
    
    def stop_auto_train_all(self):
        """Dừng auto train trên tất cả thiết bị"""
        self.running = False
        
        # Dừng tất cả AutoTrain instances
        for device_serial, auto_train in self.device_auto_trains.items():
            auto_train.stop()
            print(f"Đã gửi lệnh dừng cho thiết bị: {device_serial}")
        
        # Đợi tất cả threads kết thúc
        for device_serial, thread in self.device_threads.items():
            if thread.is_alive():
                print(f"Đang đợi thread {device_serial} kết thúc...")
                thread.join(timeout=5)  # Đợi tối đa 5 giây
        
        # Clear data
        self.device_threads.clear()
        self.device_auto_trains.clear()
        
        print("Đã dừng auto train trên tất cả thiết bị")
    
    def stop_auto_train_device(self, device_serial: str):
        """Dừng auto train trên một thiết bị cụ thể"""
        if device_serial in self.device_auto_trains:
            auto_train = self.device_auto_trains[device_serial]
            auto_train.stop()
            
            # Đợi thread kết thúc
            if device_serial in self.device_threads:
                thread = self.device_threads[device_serial]
                if thread.is_alive():
                    thread.join(timeout=5)
                del self.device_threads[device_serial]
            
            del self.device_auto_trains[device_serial]
            print(f"Đã dừng auto train cho thiết bị: {device_serial}")
            return True
        
        return False
    
    def get_all_stats(self) -> Dict[str, Dict]:
        """Lấy thống kê của tất cả thiết bị"""
        stats = {}
        
        for device_serial, auto_train in self.device_auto_trains.items():
            try:
                device_stats = auto_train.get_train_stats()
                device_stats['status'] = 'running' if auto_train.running else 'stopped'
                device_stats['thread_alive'] = (
                    device_serial in self.device_threads and 
                    self.device_threads[device_serial].is_alive()
                )
                stats[device_serial] = device_stats
            except Exception as e:
                stats[device_serial] = {
                    'error': str(e),
                    'status': 'error'
                }
        
        return stats
    
    def get_device_stats(self, device_serial: str) -> Dict:
        """Lấy thống kê của một thiết bị cụ thể"""
        if device_serial in self.device_auto_trains:
            auto_train = self.device_auto_trains[device_serial]
            stats = auto_train.get_train_stats()
            stats['status'] = 'running' if auto_train.running else 'stopped'
            stats['thread_alive'] = (
                device_serial in self.device_threads and 
                self.device_threads[device_serial].is_alive()
            )
            return stats
        
        return {'status': 'not_running'}
    
    def is_running(self) -> bool:
        """Kiểm tra có thiết bị nào đang chạy auto train không"""
        return len(self.device_auto_trains) > 0
    
    def get_running_devices(self) -> List[str]:
        """Lấy danh sách thiết bị đang chạy auto train"""
        running_devices = []
        
        for device_serial, auto_train in self.device_auto_trains.items():
            if auto_train.running:
                running_devices.append(device_serial)
        
        return running_devices
    
    def restart_device_auto_train(self, device_serial: str, train_spot_key: str):
        """Khởi động lại auto train cho một thiết bị"""
        # Dừng auto train hiện tại (nếu có)
        self.stop_auto_train_device(device_serial)
        
        # Kiểm tra thiết bị có sẵn không
        controller = self.multi_device_controller.get_device(device_serial)
        if not controller:
            print(f"Thiết bị {device_serial} không khả dụng")
            return False
        
        # Khởi động lại
        auto_train = AutoTrain(controller, self.image_rec)
        self.device_auto_trains[device_serial] = auto_train
        
        thread = threading.Thread(
            target=self._run_auto_train_for_device,
            args=(device_serial, train_spot_key),
            name=f"AutoTrain-{device_serial}"
        )
        self.device_threads[device_serial] = thread
        thread.start()
        
        print(f"Đã khởi động lại auto train cho thiết bị: {device_serial}")
        return True
    
    def pause_device_auto_train(self, device_serial: str):
        """Tạm dừng auto train cho một thiết bị"""
        if device_serial in self.device_auto_trains:
            auto_train = self.device_auto_trains[device_serial]
            auto_train.running = False
            print(f"Đã tạm dừng auto train cho thiết bị: {device_serial}")
            return True
        return False
    
    def resume_device_auto_train(self, device_serial: str):
        """Tiếp tục auto train cho một thiết bị"""
        if device_serial in self.device_auto_trains:
            auto_train = self.device_auto_trains[device_serial]
            auto_train.running = True
            print(f"Đã tiếp tục auto train cho thiết bị: {device_serial}")
            return True
        return False
    
    def get_summary_stats(self) -> Dict:
        """Lấy thống kê tổng quan"""
        all_stats = self.get_all_stats()
        
        total_devices = len(all_stats)
        running_devices = len([s for s in all_stats.values() if s.get('status') == 'running'])
        total_kills = sum([s.get('kill_count', 0) for s in all_stats.values()])
        total_deaths = sum([s.get('death_count', 0) for s in all_stats.values()])
        
        # Tính thời gian chạy trung bình
        durations = [s.get('duration', '0:00:00') for s in all_stats.values() if 'duration' in s]
        avg_duration = "N/A"
        if durations:
            # Đơn giản hóa: chỉ lấy duration đầu tiên
            avg_duration = durations[0] if durations else "0:00:00"
        
        return {
            'total_devices': total_devices,
            'running_devices': running_devices,
            'stopped_devices': total_devices - running_devices,
            'total_kills': total_kills,
            'total_deaths': total_deaths,
            'avg_duration': avg_duration,
            'kill_death_ratio': round(total_kills / max(total_deaths, 1), 2)
        }


class MultiDeviceAutoTrainManager:
    """Manager để quản lý nhiều MultiDeviceAutoTrain instances"""
    
    def __init__(self):
        self.auto_train_instances: Dict[str, MultiDeviceAutoTrain] = {}
    
    def create_instance(self, name: str, multi_device_controller, image_recognition):
        """Tạo một instance mới"""
        instance = MultiDeviceAutoTrain(multi_device_controller, image_recognition)
        self.auto_train_instances[name] = instance
        return instance
    
    def get_instance(self, name: str) -> MultiDeviceAutoTrain:
        """Lấy instance theo tên"""
        return self.auto_train_instances.get(name)
    
    def remove_instance(self, name: str):
        """Xóa instance"""
        if name in self.auto_train_instances:
            instance = self.auto_train_instances[name]
            instance.stop_auto_train_all()
            del self.auto_train_instances[name]
    
    def get_all_instances(self) -> Dict[str, MultiDeviceAutoTrain]:
        """Lấy tất cả instances"""
        return self.auto_train_instances.copy()
    
    def stop_all_instances(self):
        """Dừng tất cả instances"""
        for instance in self.auto_train_instances.values():
            instance.stop_auto_train_all()
        self.auto_train_instances.clear()
