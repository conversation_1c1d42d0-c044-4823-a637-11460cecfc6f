import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# Import các module đã tạo
from core.adb_controller import AD<PERSON>ontroller, MultiDeviceController
from core.image_recognition import ImageRecognition
from ui.main_window import MainWindow

class AutoToolApp:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.app.setStyle('Fusion')  # Modern style
        
        # Khởi tạo các components
        # Giữ lại ADBController cho backward compatibility
        self.adb_controller = ADBController()

        # Khởi tạo MultiDeviceController cho tính năng mới
        self.multi_device_controller = MultiDeviceController()

        self.image_recognition = ImageRecognition()

        # Load các template mặc định (nếu có)
        self.load_default_templates()

        # Tạo main window với ADBController (UI sẽ tự tạo MultiDeviceController)
        self.main_window = MainWindow(self.adb_controller, self.image_recognition)
        
    def load_default_templates(self):
        """Load các template thường dùng"""
        default_templates = {
            'boss_icon': 'resources/images/boss_icon.png',
            'quest_icon': 'resources/images/quest_icon.png',
            'item_icon': 'resources/images/item_icon.png',
            'skill_icon': 'resources/images/skill_icon.png',
            'npc_icon': 'resources/images/npc_icon.png',
            'map_icon': 'resources/images/map_icon.png',
        }
        
        for name, path in default_templates.items():
            try:
                self.image_recognition.load_template(name, path)
            except:
                print(f"Không thể load template {name} từ {path}")
    
    def run(self):
        """Chạy ứng dụng"""
        self.main_window.show()
        return self.app.exec_()

def main():
    # Enable high DPI scaling
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app = AutoToolApp()
    sys.exit(app.run())

if __name__ == '__main__':
    main()