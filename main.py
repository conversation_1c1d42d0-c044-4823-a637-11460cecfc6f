import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# Import các module đã tạo
from core.adb_controller import <PERSON><PERSON><PERSON>roller, MultiDeviceController
from core.image_recognition import ImageRecognition
from ui.main_window import MainWindow

class AutoToolApp:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.app.setStyle('Fusion')  # Modern style
        
        # Khởi tạo các components
        # Giữ lại ADBController cho backward compatibility
        self.adb_controller = ADBController()

        # Khởi tạo MultiDeviceController cho tính năng mới
        self.multi_device_controller = MultiDeviceController()

        self.image_recognition = ImageRecognition()

        # Tạo main window (UI sẽ tự động load templates từ folder)
        self.main_window = MainWindow(self.adb_controller, self.image_recognition)
        
# Đã loại bỏ load_default_templates - UI sẽ tự động load từ folder
    
    def run(self):
        """Chạy ứng dụng"""
        self.main_window.show()
        return self.app.exec_()

def main():
    # Enable high DPI scaling
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app = AutoToolApp()
    sys.exit(app.run())

if __name__ == '__main__':
    main()