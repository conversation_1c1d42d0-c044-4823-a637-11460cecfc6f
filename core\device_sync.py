import threading
import time
from typing import Dict, List, Callable, Any, Optional
from enum import Enum
import queue


class SyncMode(Enum):
    """<PERSON><PERSON>c chế độ đồng bộ"""
    PARALLEL = "parallel"  # Ch<PERSON>y song song độc lập
    SYNCHRONIZED = "synchronized"  # Đồng bộ hoàn toàn
    LEADER_FOLLOWER = "leader_follower"  # <PERSON><PERSON><PERSON> thiết bị dẫn đầu, c<PERSON><PERSON> thiết bị khác theo
    ROUND_ROBIN = "round_robin"  # Lần lượt từng thiết bị


class SyncAction:
    """Đ<PERSON>i diện cho một hành động cần đồng bộ"""
    
    def __init__(self, action_type: str, params: Dict[str, Any], delay: float = 0):
        self.action_type = action_type
        self.params = params
        self.delay = delay
        self.timestamp = time.time()
        self.id = f"{action_type}_{self.timestamp}"


class DeviceSynchronizer:
    """<PERSON><PERSON> thống đồng bộ hóa thiết bị"""
    
    def __init__(self):
        self.devices: Dict[str, Any] = {}
        self.sync_mode = SyncMode.PARALLEL
        self.leader_device: Optional[str] = None
        self.action_queue = queue.Queue()
        self.sync_lock = threading.Lock()
        self.sync_barrier = None
        self.running = False
        self.sync_thread = None
        
        # Callbacks cho các loại hành động
        self.action_handlers: Dict[str, Callable] = {}
        
        # Thống kê đồng bộ
        self.sync_stats = {
            'total_actions': 0,
            'successful_syncs': 0,
            'failed_syncs': 0,
            'avg_sync_time': 0
        }
    
    def register_device(self, device_id: str, device_controller):
        """Đăng ký thiết bị vào hệ thống đồng bộ"""
        with self.sync_lock:
            self.devices[device_id] = {
                'controller': device_controller,
                'status': 'active',
                'last_action': None,
                'action_count': 0,
                'sync_errors': 0
            }
            
            # Cập nhật barrier nếu cần
            if self.sync_mode == SyncMode.SYNCHRONIZED:
                self._update_sync_barrier()
    
    def unregister_device(self, device_id: str):
        """Hủy đăng ký thiết bị"""
        with self.sync_lock:
            if device_id in self.devices:
                del self.devices[device_id]
                
                # Cập nhật leader nếu cần
                if self.leader_device == device_id:
                    self._select_new_leader()
                
                # Cập nhật barrier
                if self.sync_mode == SyncMode.SYNCHRONIZED:
                    self._update_sync_barrier()
    
    def set_sync_mode(self, mode: SyncMode, leader_device: str = None):
        """Thiết lập chế độ đồng bộ"""
        with self.sync_lock:
            self.sync_mode = mode
            
            if mode == SyncMode.LEADER_FOLLOWER:
                if leader_device and leader_device in self.devices:
                    self.leader_device = leader_device
                else:
                    self._select_new_leader()
            
            elif mode == SyncMode.SYNCHRONIZED:
                self._update_sync_barrier()
    
    def register_action_handler(self, action_type: str, handler: Callable):
        """Đăng ký handler cho loại hành động"""
        self.action_handlers[action_type] = handler
    
    def execute_action(self, action: SyncAction, target_devices: List[str] = None):
        """Thực hiện hành động theo chế độ đồng bộ"""
        if not target_devices:
            target_devices = list(self.devices.keys())
        
        start_time = time.time()
        
        try:
            if self.sync_mode == SyncMode.PARALLEL:
                self._execute_parallel(action, target_devices)
            
            elif self.sync_mode == SyncMode.SYNCHRONIZED:
                self._execute_synchronized(action, target_devices)
            
            elif self.sync_mode == SyncMode.LEADER_FOLLOWER:
                self._execute_leader_follower(action, target_devices)
            
            elif self.sync_mode == SyncMode.ROUND_ROBIN:
                self._execute_round_robin(action, target_devices)
            
            # Cập nhật thống kê
            sync_time = time.time() - start_time
            self._update_stats(True, sync_time)
            
        except Exception as e:
            print(f"Lỗi thực hiện action {action.id}: {e}")
            self._update_stats(False, time.time() - start_time)
    
    def _execute_parallel(self, action: SyncAction, target_devices: List[str]):
        """Thực hiện song song trên tất cả thiết bị"""
        threads = []
        
        def execute_on_device(device_id):
            if device_id in self.devices:
                controller = self.devices[device_id]['controller']
                self._execute_single_action(controller, action)
                self.devices[device_id]['last_action'] = action
                self.devices[device_id]['action_count'] += 1
        
        for device_id in target_devices:
            thread = threading.Thread(target=execute_on_device, args=(device_id,))
            threads.append(thread)
            thread.start()
        
        # Đợi tất cả thread hoàn thành
        for thread in threads:
            thread.join()
    
    def _execute_synchronized(self, action: SyncAction, target_devices: List[str]):
        """Thực hiện đồng bộ hoàn toàn"""
        if not self.sync_barrier:
            self._update_sync_barrier()
        
        def execute_on_device(device_id):
            if device_id in self.devices:
                # Đợi tất cả thiết bị sẵn sàng
                self.sync_barrier.wait()
                
                controller = self.devices[device_id]['controller']
                self._execute_single_action(controller, action)
                self.devices[device_id]['last_action'] = action
                self.devices[device_id]['action_count'] += 1
        
        threads = []
        for device_id in target_devices:
            thread = threading.Thread(target=execute_on_device, args=(device_id,))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
    
    def _execute_leader_follower(self, action: SyncAction, target_devices: List[str]):
        """Thực hiện theo mô hình leader-follower"""
        if not self.leader_device or self.leader_device not in target_devices:
            self._select_new_leader()
        
        # Leader thực hiện trước
        if self.leader_device in self.devices:
            controller = self.devices[self.leader_device]['controller']
            self._execute_single_action(controller, action)
            self.devices[self.leader_device]['last_action'] = action
            self.devices[self.leader_device]['action_count'] += 1
        
        # Delay nhỏ
        time.sleep(0.1)
        
        # Followers thực hiện sau
        follower_devices = [d for d in target_devices if d != self.leader_device]
        if follower_devices:
            self._execute_parallel(action, follower_devices)
    
    def _execute_round_robin(self, action: SyncAction, target_devices: List[str]):
        """Thực hiện lần lượt từng thiết bị"""
        for device_id in target_devices:
            if device_id in self.devices:
                controller = self.devices[device_id]['controller']
                self._execute_single_action(controller, action)
                self.devices[device_id]['last_action'] = action
                self.devices[device_id]['action_count'] += 1
                
                # Delay giữa các thiết bị
                time.sleep(action.delay)
    
    def _execute_single_action(self, controller, action: SyncAction):
        """Thực hiện hành động trên một controller"""
        handler = self.action_handlers.get(action.action_type)
        if handler:
            handler(controller, action.params)
        else:
            # Fallback: gọi method trực tiếp trên controller
            method_name = action.action_type
            if hasattr(controller, method_name):
                method = getattr(controller, method_name)
                method(**action.params)
    
    def _update_sync_barrier(self):
        """Cập nhật barrier cho đồng bộ"""
        active_devices = len([d for d in self.devices.values() if d['status'] == 'active'])
        if active_devices > 0:
            self.sync_barrier = threading.Barrier(active_devices)
    
    def _select_new_leader(self):
        """Chọn leader mới"""
        active_devices = [device_id for device_id, info in self.devices.items() 
                         if info['status'] == 'active']
        if active_devices:
            # Chọn thiết bị có ít lỗi nhất
            self.leader_device = min(active_devices, 
                                   key=lambda d: self.devices[d]['sync_errors'])
    
    def _update_stats(self, success: bool, sync_time: float):
        """Cập nhật thống kê"""
        self.sync_stats['total_actions'] += 1
        if success:
            self.sync_stats['successful_syncs'] += 1
        else:
            self.sync_stats['failed_syncs'] += 1
        
        # Cập nhật thời gian trung bình
        total_successful = self.sync_stats['successful_syncs']
        if total_successful > 0:
            current_avg = self.sync_stats['avg_sync_time']
            self.sync_stats['avg_sync_time'] = (current_avg * (total_successful - 1) + sync_time) / total_successful
    
    def get_sync_stats(self) -> Dict[str, Any]:
        """Lấy thống kê đồng bộ"""
        return self.sync_stats.copy()
    
    def get_device_status(self) -> Dict[str, Dict[str, Any]]:
        """Lấy trạng thái tất cả thiết bị"""
        return {device_id: {
            'status': info['status'],
            'action_count': info['action_count'],
            'sync_errors': info['sync_errors'],
            'last_action': info['last_action'].id if info['last_action'] else None
        } for device_id, info in self.devices.items()}


# Các action handlers mặc định
def default_tap_handler(controller, params):
    """Handler mặc định cho tap action"""
    x = params.get('x', 0)
    y = params.get('y', 0)
    controller.tap(x, y)


def default_swipe_handler(controller, params):
    """Handler mặc định cho swipe action"""
    x1 = params.get('x1', 0)
    y1 = params.get('y1', 0)
    x2 = params.get('x2', 0)
    y2 = params.get('y2', 0)
    duration = params.get('duration', 300)
    controller.swipe(x1, y1, x2, y2, duration)


def default_input_text_handler(controller, params):
    """Handler mặc định cho input text action"""
    text = params.get('text', '')
    controller.input_text(text)


def default_key_event_handler(controller, params):
    """Handler mặc định cho key event action"""
    keycode = params.get('keycode', 0)
    controller.key_event(keycode)
