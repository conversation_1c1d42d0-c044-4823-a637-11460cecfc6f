import cv2
import numpy as np
from typing import Tuple, List, Optional

class ImageRecognition:
    def __init__(self):
        self.templates = {}
        
    def load_template(self, name: str, path: str):
        """Load template ảnh để nhận diện"""
        template = cv2.imread(path)
        if template is not None:
            self.templates[name] = template
            print(f"Đã load template: {name}")
        else:
            print(f"Không thể load template: {path}")
    
    def find_template(self, screenshot: np.ndarray, template_name: str, 
                     threshold: float = 0.8) -> Optional[Tuple[int, int]]:
        """Tìm template trong screenshot
        Returns: (x, y) tâm của vùng tìm thấy hoặc None
        """
        if template_name not in self.templates:
            print(f"Template {template_name} chưa được load")
            return None
        
        template = self.templates[template_name]
        
        # Convert sang grayscale để tăng tốc độ
        screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
        template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
        
        # Template matching
        result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
        
        # Tìm vị trí match tốt nhất
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        print(f"Max val: {max_val}")
        if max_val >= threshold:
            # Tính tọa độ tâm
            h, w = template_gray.shape
            center_x = max_loc[0] + w // 2
            center_y = max_loc[1] + h // 2
            return (center_x, center_y)
        
        return None
    
    def find_all_templates(self, screenshot: np.ndarray, template_name: str,
                          threshold: float = 0.8) -> List[Tuple[int, int]]:
        """Tìm tất cả vị trí của template"""
        if template_name not in self.templates:
            return []
        
        template = self.templates[template_name]
        screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
        template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
        
        result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
        
        # Tìm tất cả vị trí match
        locations = np.where(result >= threshold)
        h, w = template_gray.shape
        
        matches = []
        for pt in zip(*locations[::-1]):
            center_x = pt[0] + w // 2
            center_y = pt[1] + h // 2
            matches.append((center_x, center_y))
        
        # Loại bỏ các match gần nhau
        return self._remove_close_matches(matches)
    
    def _remove_close_matches(self, matches: List[Tuple[int, int]], 
                            min_distance: int = 50) -> List[Tuple[int, int]]:
        """Loại bỏ các match quá gần nhau"""
        if not matches:
            return []
        
        filtered = [matches[0]]
        
        for match in matches[1:]:
            too_close = False
            for filtered_match in filtered:
                distance = np.sqrt((match[0] - filtered_match[0])**2 + 
                                 (match[1] - filtered_match[1])**2)
                if distance < min_distance:
                    too_close = True
                    break
            
            if not too_close:
                filtered.append(match)
        
        return filtered
    
    def find_color_region(self, screenshot: np.ndarray, 
                         lower_color: Tuple[int, int, int],
                         upper_color: Tuple[int, int, int],
                         min_area: int = 100) -> List[Tuple[int, int, int, int]]:
        """Tìm vùng có màu trong khoảng cho trước
        Returns: List của (x, y, width, height)
        """
        # Chuyển sang HSV để nhận diện màu tốt hơn
        hsv = cv2.cvtColor(screenshot, cv2.COLOR_BGR2HSV)
        
        # Tạo mask
        mask = cv2.inRange(hsv, lower_color, upper_color)
        
        # Tìm contours
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        regions = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area >= min_area:
                x, y, w, h = cv2.boundingRect(contour)
                regions.append((x, y, w, h))
        
        return regions
    
    def get_text_from_region(self, screenshot: np.ndarray, 
                           x: int, y: int, w: int, h: int) -> str:
        """OCR text từ vùng chỉ định
        Cần cài thêm: pip install pytesseract
        """
        try:
            import pytesseract
            
            # Cắt vùng cần OCR
            roi = screenshot[y:y+h, x:x+w]
            
            # Tiền xử lý ảnh
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            # Tăng độ tương phản
            _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # OCR
            text = pytesseract.image_to_string(thresh, lang='vie')
            return text.strip()
        except ImportError:
            print("Cần cài đặt pytesseract để sử dụng OCR")
            return ""